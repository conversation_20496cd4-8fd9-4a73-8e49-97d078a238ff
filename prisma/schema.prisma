// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  displayName String
  bio         String?
  profileImage String?
  password    String?  // For credentials auth
  resetToken  String?  // For password reset
  resetTokenExpiry DateTime? // For password reset
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  profile     Profile?
  accounts    Account[]
  sessions    Session[]
}

model Profile {
  id              String   @id @default(cuid())
  userId          String   @unique
  slug            String   @unique
  theme           Json     // ProfileTheme object
  backgroundType  String   @default("color")
  backgroundValue String   @default("#ffffff")
  isPublic        Boolean  @default(true)
  viewCount       Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  links           Link[]
  profileViews    ProfileView[]
  linkClicks      LinkClick[]
}

model Link {
  id          String   @id @default(cuid())
  profileId   String
  title       String
  url         String
  icon        String?
  isVisible   Boolean  @default(true)
  order       Int
  clickCount  Int      @default(0)
  
  // Scheduling fields
  isScheduled     Boolean   @default(false)
  scheduleStart   DateTime?
  scheduleEnd     DateTime?
  timezone        String?   @default("UTC")
  
  // Conditional rules
  hasConditions   Boolean   @default(false)
  defaultBehavior String    @default("show") // 'show' | 'hide'
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  linkClicks  LinkClick[]
  conditions  LinkCondition[]
  
  @@index([profileId, order])
  @@index([isScheduled, scheduleStart, scheduleEnd])
}

// New LinkCondition model for conditional link rules
model LinkCondition {
  id          String   @id @default(cuid())
  linkId      String
  type        String   // 'referrer' | 'location' | 'device' | 'time'
  priority    Int      @default(0)
  isActive    Boolean  @default(true)
  
  // JSON fields for flexible rule storage
  rules       Json     // Condition-specific rules
  action      Json     // Action to take when condition matches
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  link        Link     @relation(fields: [linkId], references: [id], onDelete: Cascade)

  @@index([linkId, priority])
  @@index([type, isActive])
  @@index([linkId, priority(sort: Desc), isActive])
  @@index([priority(sort: Desc), createdAt(sort: Desc)])
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  
  @@unique([identifier, token])
}

// Analytics models for detailed tracking
model ProfileView {
  id        String   @id @default(cuid())
  profileId String
  timestamp DateTime @default(now())
  userAgent String?
  ipHash    String?  // Hashed IP for privacy
  referrer  String?
  country   String?
  
  // Enhanced visitor context fields
  city              String?
  region            String?
  timezone          String?
  deviceType        String?  // 'mobile', 'tablet', 'desktop'
  osName            String?
  browserName       String?
  isMobile          Boolean  @default(false)
  referrerSource    String?  // 'google', 'facebook', etc.
  referrerMedium    String?  // 'search', 'social', 'referral'
  isInternalReferrer Boolean @default(false)
  
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  
  @@index([profileId, timestamp])
  @@index([timestamp])
  @@index([deviceType])
  @@index([referrerSource])
  @@index([country])
}

model LinkClick {
  id        String   @id @default(cuid())
  linkId    String
  profileId String
  timestamp DateTime @default(now())
  userAgent String?
  ipHash    String?  // Hashed IP for privacy
  referrer  String?
  country   String?
  
  // Enhanced visitor context fields
  city              String?
  region            String?
  timezone          String?
  deviceType        String?  // 'mobile', 'tablet', 'desktop'
  osName            String?
  browserName       String?
  isMobile          Boolean  @default(false)
  referrerSource    String?  // 'google', 'facebook', etc.
  referrerMedium    String?  // 'search', 'social', 'referral'
  isInternalReferrer Boolean @default(false)
  
  // New fields for conditional analytics
  conditionId     String?  // Which condition triggered this link
  conditionType   String?  // Type of condition that matched
  visitorContext  Json?    // Anonymized visitor context
  
  link      Link     @relation(fields: [linkId], references: [id], onDelete: Cascade)
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  
  @@index([linkId, timestamp])
  @@index([profileId, timestamp])
  @@index([timestamp])
  @@index([conditionId])
  @@index([deviceType])
  @@index([referrerSource])
  @@index([country])
}