-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_LinkClick" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "linkId" TEXT NOT NULL,
    "profileId" TEXT NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userAgent" TEXT,
    "ipHash" TEXT,
    "referrer" TEXT,
    "country" TEXT,
    "city" TEXT,
    "region" TEXT,
    "timezone" TEXT,
    "deviceType" TEXT,
    "osName" TEXT,
    "browserName" TEXT,
    "isMobile" BOOLEAN NOT NULL DEFAULT false,
    "referrerSource" TEXT,
    "referrerMedium" TEXT,
    "isInternalReferrer" BOOLEAN NOT NULL DEFAULT false,
    "conditionId" TEXT,
    "conditionType" TEXT,
    "visitorContext" JSONB,
    CONSTRAINT "LinkClick_linkId_fkey" FOREIGN KEY ("linkId") REFERENCES "Link" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "LinkClick_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_LinkClick" ("conditionId", "conditionType", "country", "id", "ipHash", "linkId", "profileId", "referrer", "timestamp", "userAgent", "visitorContext") SELECT "conditionId", "conditionType", "country", "id", "ipHash", "linkId", "profileId", "referrer", "timestamp", "userAgent", "visitorContext" FROM "LinkClick";
DROP TABLE "LinkClick";
ALTER TABLE "new_LinkClick" RENAME TO "LinkClick";
CREATE INDEX "LinkClick_linkId_timestamp_idx" ON "LinkClick"("linkId", "timestamp");
CREATE INDEX "LinkClick_profileId_timestamp_idx" ON "LinkClick"("profileId", "timestamp");
CREATE INDEX "LinkClick_timestamp_idx" ON "LinkClick"("timestamp");
CREATE INDEX "LinkClick_conditionId_idx" ON "LinkClick"("conditionId");
CREATE INDEX "LinkClick_deviceType_idx" ON "LinkClick"("deviceType");
CREATE INDEX "LinkClick_referrerSource_idx" ON "LinkClick"("referrerSource");
CREATE INDEX "LinkClick_country_idx" ON "LinkClick"("country");
CREATE TABLE "new_ProfileView" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userAgent" TEXT,
    "ipHash" TEXT,
    "referrer" TEXT,
    "country" TEXT,
    "city" TEXT,
    "region" TEXT,
    "timezone" TEXT,
    "deviceType" TEXT,
    "osName" TEXT,
    "browserName" TEXT,
    "isMobile" BOOLEAN NOT NULL DEFAULT false,
    "referrerSource" TEXT,
    "referrerMedium" TEXT,
    "isInternalReferrer" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "ProfileView_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_ProfileView" ("country", "id", "ipHash", "profileId", "referrer", "timestamp", "userAgent") SELECT "country", "id", "ipHash", "profileId", "referrer", "timestamp", "userAgent" FROM "ProfileView";
DROP TABLE "ProfileView";
ALTER TABLE "new_ProfileView" RENAME TO "ProfileView";
CREATE INDEX "ProfileView_profileId_timestamp_idx" ON "ProfileView"("profileId", "timestamp");
CREATE INDEX "ProfileView_timestamp_idx" ON "ProfileView"("timestamp");
CREATE INDEX "ProfileView_deviceType_idx" ON "ProfileView"("deviceType");
CREATE INDEX "ProfileView_referrerSource_idx" ON "ProfileView"("referrerSource");
CREATE INDEX "ProfileView_country_idx" ON "ProfileView"("country");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
