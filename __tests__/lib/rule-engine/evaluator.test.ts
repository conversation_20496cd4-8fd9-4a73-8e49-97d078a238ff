import { describe, it, expect, beforeEach } from '@jest/globals';
import { RuleEvaluator } from '@/lib/rule-engine/evaluator';
import { LinkWithConditions } from '@/lib/types';
import { VisitorContext } from '@/lib/rule-engine/conditions';

describe('RuleEvaluator', () => {
  let evaluator: RuleEvaluator;

  beforeEach(() => {
    evaluator = new RuleEvaluator();
  });

  // Mock data
  const mockLink: LinkWithConditions = {
    id: '1',
    profileId: '1',
    title: 'Test Link',
    url: 'https://example.com',
    icon: null,
    isVisible: true,
    order: 1,
    clickCount: 0,
    isScheduled: false,
    scheduleStart: null,
    scheduleEnd: null,
    timezone: 'UTC',
    hasConditions: false,
    defaultBehavior: 'show',
    createdAt: new Date(),
    updatedAt: new Date(),
    conditions: [],
  };

  const mockContext: VisitorContext = {
    referrer: 'https://google.com',
    location: { country: 'US', region: 'CA', city: 'San Francisco' },
    device: { type: 'desktop', platform: 'macOS', browser: 'Chrome' },
    now: new Date('2025-09-18T12:00:00Z'),
    timezone: 'America/Los_Angeles',
  };

  // Schedule Tests
  describe('Schedule Evaluation', () => {
    it('should show a link that is not scheduled', () => {
      const result = evaluator.evaluate(mockLink, mockContext);
      expect(result.shouldShow).toBe(true);
    });

    it('should not show a link before its scheduled start time', () => {
      const link = { ...mockLink, isScheduled: true, scheduleStart: new Date('2025-09-19T00:00:00Z') };
      const result = evaluator.evaluate(link, mockContext);
      expect(result.shouldShow).toBe(false);
    });

    it('should not show a link after its scheduled end time', () => {
      const link = { ...mockLink, isScheduled: true, scheduleEnd: new Date('2025-09-17T00:00:00Z') };
      const result = evaluator.evaluate(link, mockContext);
      expect(result.shouldShow).toBe(false);
    });

    it('should show a link within its scheduled time', () => {
      const link = { ...mockLink, isScheduled: true, scheduleStart: new Date('2025-09-18T00:00:00Z'), scheduleEnd: new Date('2025-09-19T00:00:00Z') };
      const result = evaluator.evaluate(link, mockContext);
      expect(result.shouldShow).toBe(true);
    });
  });

  // Referrer Tests
  describe('Referrer Condition', () => {
    it('should match an exact referrer', () => {
      const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'referrer', priority: 0, isActive: true, rules: { match: 'exact', value: 'https://google.com' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
      const result = evaluator.evaluate(link, mockContext);
      expect(result.shouldShow).toBe(true);
    });

    it('should match a referrer that contains a value', () => {
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'referrer', priority: 0, isActive: true, rules: { match: 'contains', value: 'google' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });

    it('should match a referrer with a regex', () => {
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'referrer', priority: 0, isActive: true, rules: { match: 'regex', value: '^https://.*\\.com$' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });
  });

  // Location Tests
  describe('Location Condition', () => {
    it('should match a country', () => {
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'location', priority: 0, isActive: true, rules: { match: 'country', value: 'US' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });
  });

  // Device Tests
  describe('Device Condition', () => {
    it('should match a device type', () => {
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'device', priority: 0, isActive: true, rules: { match: 'type', value: 'desktop' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });
  });

  // Time Tests
  describe('Time Condition', () => {
    it('should match the day of the week', () => {
        // Thursday is 4
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'time', priority: 0, isActive: true, rules: { dayOfWeek: 4 }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });

    it('should match a time range', () => {
        // 12:00 in LA is 19:00 UTC, but we are using local time in the evaluator
        const localContext = { ...mockContext, now: new Date('2025-09-18T12:00:00-07:00') };
        const link = { ...mockLink, hasConditions: true, conditions: [{ id: 'c1', linkId: '1', type: 'time', priority: 0, isActive: true, rules: { startTime: '10:00', endTime: '14:00' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, localContext);
        expect(result.shouldShow).toBe(true);
    });
  });

  // Default Behavior
  describe('Default Behavior', () => {
    it('should show if default is "show" and no conditions match', () => {
        const link = { ...mockLink, hasConditions: true, defaultBehavior: 'show', conditions: [{ id: 'c1', linkId: '1', type: 'referrer', priority: 0, isActive: true, rules: { match: 'exact', value: 'https://bing.com' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(true);
    });

    it('should hide if default is "hide" and no conditions match', () => {
        const link = { ...mockLink, hasConditions: true, defaultBehavior: 'hide', conditions: [{ id: 'c1', linkId: '1', type: 'referrer', priority: 0, isActive: true, rules: { match: 'exact', value: 'https://bing.com' }, action: {}, createdAt: new Date(), updatedAt: new Date() }] };
        const result = evaluator.evaluate(link, mockContext);
        expect(result.shouldShow).toBe(false);
    });
  });
});
