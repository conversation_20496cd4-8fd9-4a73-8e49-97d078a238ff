import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { UserRepository } from '@/lib/repositories/user'
import { usernameSchema } from '@/lib/validations'
import { performanceMonitor } from '@/lib/utils/performance-monitor'
import { z } from 'zod'

// Rate limiting map to track requests per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute per user

// Request timeout duration
const REQUEST_TIMEOUT = 5000 // 5 seconds

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_FORMAT: 'INVALID_FORMAT',
  RATE_LIMITED: 'RATE_LIMITED',
  TIMEOUT: 'TIMEOUT',
  SERVER_ERROR: 'SERVER_ERROR',
  MISSING_PARAMETER: 'MISSING_PARAMETER'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface UsernameAvailabilityResponse {
  username: string
  available: boolean
  cached?: boolean
}

interface UsernameAvailabilityError {
  error: string
  code: ErrorCode
  details?: string[]
  retryAfter?: number
}

// Utility function to create timeout promise
function createTimeoutPromise(ms: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), ms)
  })
}

// Utility function to check rate limiting
function checkRateLimit(userId: string): { allowed: boolean; retryAfter?: number } {
  const now = Date.now()
  const userLimit = rateLimitMap.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or initialize rate limit
    rateLimitMap.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return { allowed: true }
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000)
    return { allowed: false, retryAfter }
  }

  // Increment count
  userLimit.count++
  return { allowed: true }
}

// Enhanced logging function with performance monitoring integration
function logRequest(
  userId: string,
  username: string,
  duration: number,
  success: boolean,
  error?: string,
  errorCode?: ErrorCode,
  metadata?: Record<string, any>
) {
  const logData = {
    timestamp: new Date().toISOString(),
    userId,
    username,
    duration,
    success,
    error,
    errorCode,
    endpoint: '/api/account/username/availability',
    ...metadata
  }
  
  // Log to console for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Username availability check:', JSON.stringify(logData))
  }
  
  // Record performance metrics
  performanceMonitor.recordPerformance(
    'api-username-availability',
    duration,
    success,
    error,
    {
      userId,
      username,
      errorCode,
      ...metadata
    }
  )
  
  // Record error metrics if applicable
  if (!success && error && errorCode) {
    performanceMonitor.recordError(
      'api-username-availability',
      error,
      errorCode,
      {
        userId,
        username,
        duration,
        ...metadata
      }
    )
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  let userId: string | undefined
  let username: string | undefined

  try {
    // Authentication check
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: UsernameAvailabilityError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED
      }
      return NextResponse.json(error, { 
        status: 401,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
    }

    userId = session.user.id

    // Rate limiting check
    const rateLimit = checkRateLimit(userId)
    if (!rateLimit.allowed) {
      const error: UsernameAvailabilityError = {
        error: 'Too many requests',
        code: ERROR_CODES.RATE_LIMITED,
        retryAfter: rateLimit.retryAfter
      }
      
      logRequest(
        userId, 
        username || '', 
        Date.now() - startTime, 
        false, 
        'Rate limited',
        ERROR_CODES.RATE_LIMITED,
        { retryAfter: rateLimit.retryAfter }
      )
      
      return NextResponse.json(error, { 
        status: 429,
        headers: {
          'Retry-After': rateLimit.retryAfter?.toString() || '60',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    // Extract and validate username parameter
    const { searchParams } = new URL(request.url)
    username = searchParams.get('username') || undefined

    if (!username) {
      const error: UsernameAvailabilityError = {
        error: 'Username parameter is required',
        code: ERROR_CODES.MISSING_PARAMETER
      }
      
      logRequest(
        userId, 
        '', 
        Date.now() - startTime, 
        false, 
        'Missing username parameter',
        ERROR_CODES.MISSING_PARAMETER
      )
      
      return NextResponse.json(error, { 
        status: 400,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    // Validate username format
    try {
      usernameSchema.parse(username)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationError: UsernameAvailabilityError = {
          error: 'Invalid username format',
          code: ERROR_CODES.INVALID_FORMAT,
          details: error.issues.map(issue => issue.message)
        }
        
        logRequest(
          userId, 
          username, 
          Date.now() - startTime, 
          false, 
          'Invalid format',
          ERROR_CODES.INVALID_FORMAT,
          { validationErrors: error.issues.map(issue => issue.message) }
        )
        
        return NextResponse.json(validationError, { 
          status: 400,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        })
      }
    }

    // Check availability with timeout
    const availabilityPromise = UserRepository.isUsernameAvailable(username, userId)
    const timeoutPromise = createTimeoutPromise(REQUEST_TIMEOUT)

    let isAvailable: boolean
    try {
      isAvailable = await Promise.race([availabilityPromise, timeoutPromise])
    } catch (error) {
      if (error instanceof Error && error.message === 'Request timeout') {
        const timeoutError: UsernameAvailabilityError = {
          error: 'Request timeout - please try again',
          code: ERROR_CODES.TIMEOUT
        }
        
        logRequest(
          userId, 
          username, 
          Date.now() - startTime, 
          false, 
          'Timeout',
          ERROR_CODES.TIMEOUT,
          { timeoutDuration: REQUEST_TIMEOUT }
        )
        
        return NextResponse.json(timeoutError, { 
          status: 408,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        })
      }
      throw error // Re-throw other errors to be handled by outer catch
    }

    const response: UsernameAvailabilityResponse = {
      username,
      available: isAvailable
    }

    logRequest(
      userId, 
      username, 
      Date.now() - startTime, 
      true,
      undefined,
      undefined,
      { available: isAvailable, cached: false }
    )

    return NextResponse.json(response, {
      status: 200,
      headers: {
        // Cache successful responses for 30 seconds
        'Cache-Control': 'private, max-age=30, stale-while-revalidate=60',
        'ETag': `"${username}-${isAvailable}-${Math.floor(Date.now() / 30000)}"`,
        'Vary': 'Authorization'
      }
    })

  } catch (error) {
    console.error('Username availability check error:', error)
    
    const serverError: UsernameAvailabilityError = {
      error: 'Internal server error - please try again later',
      code: ERROR_CODES.SERVER_ERROR
    }

    if (userId && username) {
      logRequest(
        userId, 
        username, 
        Date.now() - startTime, 
        false, 
        'Server error',
        ERROR_CODES.SERVER_ERROR,
        { errorDetails: error instanceof Error ? error.message : 'Unknown error' }
      )
    }

    return NextResponse.json(serverError, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
  }
}