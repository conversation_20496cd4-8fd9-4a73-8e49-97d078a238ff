import type { <PERSON>ada<PERSON>, View<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import { <PERSON><PERSON>rovider } from "next-auth/react";
import { Toaster } from "sonner";
import { AppErrorBoundary } from "@/components/error-boundaries/app-error-boundary";
import { PerformanceProvider } from "@/components/providers/performance-provider";
import { SkipLinks } from "@/components/ui/skip-links";
import "./globals.css";

// Import accessibility checks for development
if (process.env.NODE_ENV === 'development') {
  import('@/lib/utils/accessibility-test').then(({ initAccessibilityChecks }) => {
    initAccessibilityChecks()
  })
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap", // Optimize font loading
  preload: true,
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  preload: false, // Only preload primary font
});

export const metadata: Metadata = {
  title: "LinksInBio",
  description: "Create your personalized link-in-bio page",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SkipLinks />
        <AppErrorBoundary level="app" context="root-layout">
          <PerformanceProvider>
            <SessionProvider>
              {children}
              <Toaster 
                position="top-right" 
                toastOptions={{
                  // Ensure toasts are accessible
                  role: 'status',
                  'aria-live': 'polite',
                }}
              />
            </SessionProvider>
          </PerformanceProvider>
        </AppErrorBoundary>
      </body>
    </html>
  );
}
