import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import { DashboardNav } from '@/components/dashboard/dashboard-nav'
import { DashboardHeader } from '@/components/dashboard/dashboard-header'
import { DashboardErrorBoundary } from '@/components/dashboard/dashboard-error-boundary'
import { PageErrorBoundary } from '@/components/error-boundaries/app-error-boundary'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth()

  if (!session?.user) {
    redirect('/auth/signin')
  }

  // Extract only the plain data we need for the client component
  const userData = {
    name: session.user.name,
    email: session.user.email,
    image: session.user.image,
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader user={userData} />
      <div className="flex min-h-[calc(100vh-4rem)]">
        <DashboardNav />
        <main 
          id="main-content" 
          className="flex-1 p-4 sm:p-6  w-full max-w-full overflow-x-hidden"
          role="main"
          aria-label="Dashboard content"
        >
          <PageErrorBoundary context="dashboard">
            <DashboardErrorBoundary>
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </DashboardErrorBoundary>
          </PageErrorBoundary>
        </main>
      </div>
    </div>
  )
}