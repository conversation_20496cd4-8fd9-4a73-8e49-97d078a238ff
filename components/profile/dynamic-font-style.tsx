"use client"

import { useLayoutEffect } from 'react'

interface DynamicFontStyleProps {
  fontFamily: string
}

export function DynamicFontStyle({ fontFamily }: DynamicFontStyleProps) {
  useLayoutEffect(() => {
    if (fontFamily) {
      const fontName = fontFamily.split(' ').join('+')
      const link = document.createElement('link')
      link.href = `https://fonts.googleapis.com/css2?family=${fontName}:wght@400;700&display=swap`
      link.rel = 'stylesheet'
      document.head.appendChild(link)

      return () => {
        document.head.removeChild(link)
      }
    }
  }, [fontFamily])

  return null
}
