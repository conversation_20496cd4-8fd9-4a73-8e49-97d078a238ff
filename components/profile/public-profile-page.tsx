"use client"

import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { ScreenReaderOnly } from '@/components/ui/skip-links'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { OptimizedImage } from '@/components/ui/optimized-image'
import { cn } from '@/lib/utils'
import { ThemeProvider } from './theme-provider'
import { ProfileLinkCard } from './profile-link-card'
import { DynamicFontStyle } from './dynamic-font-style'
import { filterActiveLinks } from '@/lib/utils/link-scheduling'
import type { ProfileWithLinks, ProfileTheme, Link as LinkType } from '@/lib/types'

interface PublicProfilePageProps {
  profile: ProfileWithLinks
  className?: string
}

export function PublicProfilePage({ profile, className }: PublicProfilePageProps) {
  const theme = profile.theme as unknown as ProfileTheme
  const visibleLinks = filterActiveLinks(profile.links)

  return (
    <ThemeProvider theme={theme} backgroundType={profile.backgroundType} backgroundValue={profile.backgroundValue}>
      <DynamicFontStyle fontFamily={theme.fontFamily} />
      <div className={cn("min-h-screen w-full", className)}>
        <main
          id="main-content"
          className="container mx-auto px-4 py-6 sm:py-8 max-w-sm sm:max-w-md safe-area-inset-top safe-area-inset-bottom"
          role="main"
          aria-label={`${profile.user?.displayName}'s profile page`}
        >
          <div className="text-center space-y-4 sm:space-y-6">
            {/* Profile Header */}
            <header className="space-y-3 sm:space-y-4">
              {/* Profile Image */}
              <div className="mx-auto">
                <OptimizedImage
                  src={profile.user?.profileImage}
                  alt={`Profile picture of ${profile.user?.displayName}`}
                  width={96}
                  height={96}
                  className="rounded-full border-2 w-20 h-20 sm:w-24 sm:h-24 mx-auto mt-6"
                  quality={90}
                  priority
                  sizes="(max-width: 640px) 80px, 96px"
                  fallback={
                    <Avatar className="w-20 h-20 sm:w-24 sm:h-24 mx-auto border-2" style={{ borderColor: theme.primaryColor }}>
                      <AvatarFallback
                        style={{
                          backgroundColor: theme.primaryColor,
                          color: theme.backgroundColor
                        }}
                        className="text-base sm:text-lg font-semibold"
                        aria-label={`Profile picture placeholder for ${profile.user?.displayName}`}
                      >
                        {profile.user?.displayName.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  }
                />
              </div>

              {/* Name */}
              <h1
                className="text-xl sm:text-2xl font-bold px-2"
                style={{ color: theme.textColor }}
              >
                {profile.user?.displayName || ""}
              </h1>

              {/* Bio */}
              {profile.user?.bio && (
                <p
                  className="text-sm sm:text-base leading-relaxed opacity-90 max-w-xs sm:max-w-sm mx-auto px-4"
                  style={{ color: theme.textColor }}
                >
                  {profile.user.bio}
                </p>
              )}
            </header>

            {/* Links */}
            <section aria-label="Links" className="w-full">
              {visibleLinks.length > 0 ? (
                <nav className="space-y-2 sm:space-y-3 w-full px-2" aria-label={`${profile.user.displayName}'s links`}>
                  <ScreenReaderOnly>
                    {visibleLinks.length} link{visibleLinks.length !== 1 ? 's' : ''} available
                  </ScreenReaderOnly>
                  {visibleLinks.map((link: LinkType, index: number) => (
                    <ProfileLinkCard
                      key={link.id}
                      link={link}
                      theme={theme}
                      index={index + 1}
                      total={visibleLinks.length}
                    />
                  ))}
                </nav>
              ) : (
                <div className="py-6 sm:py-8" role="status">
                  <p
                    className="text-sm opacity-60"
                    style={{ color: theme.textColor }}
                  >
                    No links available
                  </p>
                </div>
              )}
            </section>

            {/* Footer */}
            <footer className="pt-8 space-y-2" id="footer">
              {theme.preset && (
                <Badge
                  variant="secondary"
                  className="text-xs opacity-70"
                  style={{
                    backgroundColor: theme.secondaryColor,
                    color: theme.backgroundColor
                  }}
                  aria-label={`Using ${theme.preset} theme`}
                >
                  {theme.preset} theme
                </Badge>
              )}

              <div className="flex items-baseline justify-center space-x-1 text-sm opacity-50">
                <span style={{ color: theme.textColor }}>Powered by</span>
                <Link
                  href="/"
                  className="hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring rounded-sm"
                  style={{ color: theme.primaryColor }}
                  aria-label="Visit LinksInBio homepage"
                >
                  LinksInBio
                </Link>
              </div>
            </footer>
          </div>
        </main>
      </div>
    </ThemeProvider>
  )
}