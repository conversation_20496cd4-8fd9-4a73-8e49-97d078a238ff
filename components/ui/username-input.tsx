"use client"

import * as React from "react"
import { X, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { usernameSchema } from "@/lib/validations"
import { UsernameErrorDisplay, InlineUsernameError } from "@/components/ui/username-error-display"
import { UsernameSuccessDisplay, UsernameSuccessIcon } from "@/components/ui/username-success-display"
import { 
  getErrorCodeFromMessage,
  allowsGracefulDegradation 
} from "@/lib/constants/username-errors"

interface UsernameInputProps extends Omit<React.ComponentProps<"input">, "onChange"> {
  value: string
  onChange: (value: string) => void
  currentUsername?: string
  disabled?: boolean
  showRequirements?: boolean
  showRetryButton?: boolean
  showGracefulDegradation?: boolean
  className?: string
  status?: { 
    checking: boolean
    available?: boolean
    error?: string
    errorCode?: string
    cached?: boolean
    retryCount?: number
  }
  checkAvailability?: (username: string) => void
  retry?: () => void
  canRetry?: boolean
  maxRetries?: number
  reset?: () => void
}

const USERNAME_REQUIREMENTS = [
  "3-30 characters long",
  "Only letters, numbers, hyphens, and underscores",
  "Cannot start or end with special characters",
  "No consecutive special characters"
]

export function UsernameInput({
  value,
  onChange,
  currentUsername = "",
  disabled = false,
  showRequirements = true,
  showRetryButton = true,
  showGracefulDegradation = false,
  className,
  status,
  ...props
}: UsernameInputProps) {
  const [focused, setFocused] = React.useState(false)
  const [validationError, setValidationError] = React.useState<string>("")
  const [validationErrorCode, setValidationErrorCode] = React.useState<string>("")
  
  const actualStatus = status || { checking: false }
  const checkAvailability = props?.checkAvailability
  const reset = props?.reset
  const retry = props?.retry
  const canRetry = props?.canRetry
  const maxRetries = props?.maxRetries

  // Handle input changes
  const handleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)

    // Reset states
    setValidationError("")
    setValidationErrorCode("")
    reset?.()

    // Skip validation for empty values
    if (!newValue.trim()) {
      return
    }

    // Validate format first
    const validation = usernameSchema.safeParse(newValue)
    if (!validation.success) {
      const firstError = validation.error?.issues?.[0]?.message || "Invalid username format"
      const errorCode = getErrorCodeFromMessage(firstError)
      setValidationError(firstError)
      setValidationErrorCode(errorCode)
      return
    }

    // Check availability if format is valid
    checkAvailability?.(newValue)
  }, [onChange, checkAvailability, reset])

  // Determine status indicator
  const getStatusIndicator = () => {
    if (!value.trim()) return null
    
    if (validationError) {
      return (
        <AlertCircle 
          className="h-4 w-4 text-destructive" 
          aria-label="Validation error"
        />
      )
    }

    if (actualStatus?.checking) {
      return (
        <UsernameSuccessIcon 
          type="checking"
          className="h-4 w-4"
        />
      )
    }

    if (actualStatus?.error) {
      return (
        <X 
          className="h-4 w-4 text-destructive" 
          aria-label="Username unavailable"
        />
      )
    }

    if (actualStatus?.available === true) {
      return (
        <UsernameSuccessIcon 
          type={actualStatus?.cached ? "cached" : "available"}
          className="h-4 w-4"
        />
      )
    }

    return null
  }

  // Handle retry functionality
  const handleRetry = React.useCallback(() => {
    if (retry && value.trim()) {
      retry()
    }
  }, [retry, value])

  // Determine input styling based on status
  const getInputClassName = () => {
    if (!value.trim()) return ""
    
    // Check if error allows graceful degradation
    const hasGracefulError = actualStatus.errorCode && allowsGracefulDegradation(actualStatus.errorCode as any)
    
    if (validationError || (actualStatus.error && !hasGracefulError) || actualStatus.available === false) {
      return "border-destructive focus-visible:border-destructive focus-visible:ring-destructive/20"
    }
    
    if (hasGracefulError) {
      return "border-amber-500 focus-visible:border-amber-500 focus-visible:ring-amber-500/20"
    }
    
    if (actualStatus.available === true) {
      return "border-green-600 focus-visible:border-green-600 focus-visible:ring-green-600/20"
    }
    
    return ""
  }

  const statusIndicator = getStatusIndicator()

  return (
    <div className={cn("space-y-2", className)}>
      <div className="relative">
        <Input
          {...props}
          type="text"
          value={value}
          onChange={handleChange}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          disabled={disabled}
          className={cn(
            "pr-10", // Space for status indicator
            getInputClassName()
          )}
          aria-describedby={
            showRequirements && focused 
              ? "username-requirements username-status" 
              : "username-status"
          }
          aria-invalid={value.trim() ? !!(validationError || actualStatus.error || actualStatus.available === false) : false}
          // autoComplete="username"
          spellCheck={false}
        />
        
        {/* Status indicator */}
        {statusIndicator && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {statusIndicator}
          </div>
        )}
      </div>

      {/* Status messages */}
      <div id="username-status">
        {/* Validation errors */}
        {validationError && (
          <InlineUsernameError 
            errorCode={validationErrorCode as any}
            errorMessage={validationError}
          />
        )}
        
        {/* Availability status */}
        {!validationError && actualStatus?.checking && (
          <UsernameSuccessDisplay type="checking" />
        )}
        
        {!validationError && actualStatus?.available === true && (
          <UsernameSuccessDisplay 
            type={actualStatus?.cached ? "cached" : "available"} 
          />
        )}
        
        {/* Error handling with retry functionality */}
        {!validationError && actualStatus?.error && (
          <UsernameErrorDisplay
            errorCode={actualStatus?.errorCode as any}
            errorMessage={actualStatus?.error}
            onRetry={canRetry ? handleRetry : undefined}
            retryCount={actualStatus.retryCount || 0}
            maxRetries={maxRetries}
            isRetrying={actualStatus.checking}
            showRetryButton={showRetryButton}
            showGracefulDegradation={showGracefulDegradation}
          />
        )}
      </div>

      {/* Requirements display */}
      {showRequirements && focused && (
        <div 
          id="username-requirements"
          className="rounded-md border bg-muted/50 p-3"
          role="region"
          aria-label="Username requirements"
        >
          <p className="text-sm font-medium text-foreground mb-2">
            Username requirements:
          </p>
          <ul className="text-sm text-muted-foreground space-y-1">
            {USERNAME_REQUIREMENTS.map((requirement, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-muted-foreground mt-0.5">•</span>
                <span>{requirement}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}