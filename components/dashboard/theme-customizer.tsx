"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ColorPicker } from '@/components/ui/color-picker'
import { FontSelector } from '@/components/ui/font-selector'
import { BackgroundSelector } from '@/components/ui/background-selector'
import { ThemePreview } from '@/components/dashboard/theme-preview'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { useThemeStore } from '@/lib/stores/theme-store'
import { updateTheme, applyThemePreset, getCurrentTheme } from '@/lib/actions/theme'
import { Palette, Type, Image, Save, RotateCcw } from 'lucide-react'
import { toast } from 'sonner'
import type { ProfileTheme } from '@/lib/types'

interface ThemeCustomizerProps {
  user?: {
    displayName: string
    bio?: string | null
    profileImage?: string | null
  }
}

export function ThemeCustomizer({ user }: ThemeCustomizerProps) {
  const {
    currentTheme,
    presets,
    backgroundType,
    backgroundValue,
    isPreviewMode,
    setTheme,
    updateThemeProperty,
    applyPreset,
    setBackgroundType,
    setBackgroundValue,
    setPreviewMode,
    resetTheme
  } = useThemeStore()

  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  // Load current theme on mount
  useEffect(() => {
    const loadCurrentTheme = async () => {
      setIsLoading(true)
      try {
        const result = await getCurrentTheme()
        if (result.success && result.data) {
          setTheme(result.data.theme)
          setBackgroundType(result.data.backgroundType || 'color')
          setBackgroundValue(result.data.backgroundValue || result.data.theme.backgroundColor)
        }
      } catch (error) {
        console.error('Failed to load theme:', error)
        toast.error('Failed to load current theme')
      } finally {
        setIsLoading(false)
        setIsInitialized(true)
      }
    }

    loadCurrentTheme()
  }, [setTheme, setBackgroundType, setBackgroundValue])

  const handlePresetSelect = async (presetName: string) => {
    setIsLoading(true)
    try {
      const result = await applyThemePreset(presetName.toLowerCase())
      if (result.success) {
        applyPreset(presetName)
        toast.success(`${presetName} theme applied!`)
      } else {
        toast.error(result.error || 'Failed to apply preset')
      }
    } catch (error) {
      console.error('Failed to apply preset:', error)
      toast.error('Failed to apply preset')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveTheme = async () => {
    setIsSaving(true)
    try {
      const result = await updateTheme({
        theme: currentTheme,
        backgroundType,
        backgroundValue,
      })
      
      if (result.success) {
        toast.success('Theme saved successfully!')
      } else {
        toast.error(result.error || 'Failed to save theme')
      }
    } catch (error) {
      console.error('Failed to save theme:', error)
      toast.error('Failed to save theme')
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    resetTheme()
    toast.success('Theme reset to default')
  }

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:items-start">
      {/* Customization Controls */}
      <div className="space-y-6 lg:pr-4">
        {/* Theme Presets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2" />
              Theme Presets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {presets.map((preset) => (
                <div
                  key={preset.name}
                  className="relative p-4 border rounded-lg cursor-pointer hover:border-primary transition-colors"
                  onClick={() => handlePresetSelect(preset.name)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{preset.name}</span>
                    {currentTheme.preset === preset.theme.preset && (
                      <Badge variant="secondary">Active</Badge>
                    )}
                  </div>
                  <div 
                    className="h-8 rounded"
                    style={{
                      background: `linear-gradient(135deg, ${preset.theme.primaryColor} 0%, ${preset.theme.secondaryColor} 100%)`
                    }}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Custom Colors */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2" />
              Custom Colors
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <ColorPicker
              label="Primary Color"
              value={currentTheme.primaryColor}
              onChange={(color) => updateThemeProperty('primaryColor', color)}
            />
            <ColorPicker
              label="Secondary Color"
              value={currentTheme.secondaryColor}
              onChange={(color) => updateThemeProperty('secondaryColor', color)}
            />
            <ColorPicker
              label="Text Color"
              value={currentTheme.textColor}
              onChange={(color) => updateThemeProperty('textColor', color)}
            />
          </CardContent>
        </Card>

        {/* Background */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Image className="h-5 w-5 mr-2" />
              Background
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BackgroundSelector
              type={backgroundType}
              value={backgroundValue}
              onTypeChange={setBackgroundType}
              onValueChange={setBackgroundValue}
            />
          </CardContent>
        </Card>

        {/* Typography */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Type className="h-5 w-5 mr-2" />
              Typography
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FontSelector
              value={currentTheme.fontFamily}
              onChange={(font) => updateThemeProperty('fontFamily', font)}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex space-x-3">
          <Button 
            onClick={handleSaveTheme} 
            disabled={isSaving}
            className="flex-1"
          >
            {isSaving ? (
              <LoadingSpinner className="h-4 w-4 mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Theme
          </Button>
          <Button 
            variant="outline" 
            onClick={handleReset}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>
      </div>

      {/* Preview */}
      <div
        className="lg:h-fit lg:z-10"
        style={{
          position: 'sticky',
          top: '1.5rem',
          alignSelf: 'flex-start'
        }}
      >
        <ThemePreview
          theme={currentTheme}
          backgroundType={backgroundType}
          backgroundValue={backgroundValue}
          user={user}
        />
      </div>
    </div>
  )
}