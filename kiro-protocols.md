# AI Agent Protocol: The .kiro Methodology

This document outlines the standard operating protocol for translating a user's natural language request into a structured, actionable, and trackable development plan. Adherence to this protocol is mandatory for all new feature development and project scaffolding tasks.

## 1. Core Philosophy

The `.kiro` methodology is designed to bridge the gap between human intent and machine execution. Its purpose is to:

1.  **Create Clarity:** Deconstruct ambiguous requests into concrete, verifiable tasks.
2.  **Ensure Traceability:** Create a clear link from a high-level requirement down to the specific tasks that implement it.
3.  **Enable Tracking:** Provide a single source of truth for the status of any given feature, readable by both humans and AI.

## 2. The `.kiro` Directory Structure

All planning artifacts must reside within the `.kiro/specs/` directory. For each new feature or problem, a new subdirectory is created.

-   **`.kiro/specs/<feature-name>/`**: A dedicated folder for a single feature, named in `kebab-case`.
    -   **`requirements.md` (The "Why"):** Outlines the user's goals and the problems to be solved.
    -   **`design.md` (The "How"):** Details the proposed technical implementation.
    -   **`tasks.md` (The "What"):** A prioritized, actionable checklist of all implementation steps.

---

## 3. The Three-Phase Protocol

### Phase 1: Understand and Deconstruct

Your first objective is to achieve absolute clarity on the user's request before generating any files.

**Step 1.1: Receive and Parse the User's Request**
Analyze the initial natural language prompt to identify the core subject and intent.

**Step 1.2: Ask Clarifying Questions (Mandatory)**
Before proceeding, you must identify ambiguities and ask the user clarifying questions to resolve them. This is the most critical step to ensure the final plan is accurate.

*   **Example Questions:**
    *   *For a feature:* "What should happen if [edge case] occurs?"
    *   *For UI:* "Should this be responsive? How should it look on mobile?"
    *   *For data:* "What are the validation rules for this input?"
    *   *For behavior:* "When this task is complete, what is the expected outcome for the user?"

**Step 1.3: Identify Core Technical Components**
Based on the clarified request, mentally categorize the required work into distinct areas:
-   Database (Schema changes, migrations)
-   Backend (API routes, Server Actions, business logic)
-   Frontend (UI components, state management, user flow)
-   Testing (Unit, integration, end-to-end)
-   Deployment (Environment variables, configuration)

### Phase 2: Generate the Plan

Once the request is fully understood, generate the three planning documents inside a new feature directory.

**Step 2.1: Create `requirements.md`**
-   **Purpose:** To document the "Why".
-   **Format:** Use user stories (`As a [user type], I want to [action], so that [benefit].`).
-   **Traceability:** Assign a unique, sequential ID to each requirement (e.g., `REQ-1.1`, `REQ-1.2`).

**Step 2.2: Create `design.md`**
-   **Purpose:** To document the "How".
-   **Content:** Detail the technical approach. Specify database model changes, new functions or classes, API contracts, component names, and key libraries to be used.

**Step 2.3: Create `tasks.md`**
-   **Purpose:** To document the "What". This is the master execution plan.
-   **Format:** Use a Markdown checklist.
-   **Prioritization:** Order tasks logically. Database and backend work should generally precede frontend work.
-   **Status:**
    -   `[ ]`: The task is not yet implemented.
    -   `[x]`: The task has been successfully implemented and verified.
-   **Traceability:** At the end of each major task or group of sub-tasks, add a line referencing the requirement IDs it fulfills (e.g., `_Requirements: REQ-1.1, RE-2.2_`).

### Phase 3: Execute and Track

This phase governs the cycle of implementing code and updating the plan.

**Step 3.1: Await User Command for Execution**
Do not begin coding until the user instructs you to proceed with a specific task from the `tasks.md` file.

**Step 3.2: Implement the Code**
Write the code to fulfill the specified task, following the plan laid out in `design.md`.

**Step 3.3: Update Task Status (Mandatory)**
Immediately after the code for a task has been successfully generated and confirmed, you **must** update its status in the corresponding `tasks.md` file by changing `[ ]` to `[x]`. This provides a real-time feedback loop on project progress.

---

### Example Walkthrough

**User:** "I need to be able to delete my account."

**Agent (Phase 1):** "Understood. To clarify:
1.  Should the user be required to type their username to confirm the deletion?
2.  What should happen to the user's public profile URL after deletion? Should it show a 'not found' page?
3.  Will this action also delete all associated data, like links and profile customizations?"

**Agent (Phase 2):** *(After getting answers, the agent creates the following files in `.kiro/specs/account-deletion/`)*

-   **`requirements.md`:**
    -   `REQ-1.1`: As a user, I want to permanently delete my account and all associated data.
    -   `REQ-1.2`: As a user, I must confirm this action to prevent accidental deletion.

-   **`design.md`:**
    -   **Backend:** Create a `deleteAccount` Server Action. This action will use `UserRepository.delete(userId)`. The database schema should have a cascading delete rule from `User` to `Profile` and `Link`.
    -   **Frontend:** Create a "Delete Account" section in settings with a button that opens a confirmation modal.

-   **`tasks.md`:**
    -   `[ ] 1. Update Database Schema`
        -   `[ ]` Ensure `onDelete: Cascade` is set on the `User` relations in `schema.prisma`.
        -   _Requirements: REQ-1.1_
    -   `[ ] 2. Implement Backend Logic`
        -   `[ ]` Create the `deleteAccount` Server Action.
        -   _Requirements: REQ-1.1_
    -   `[ ] 3. Build Frontend UI`
        -   `[ ]` Add a "Delete Account" button to the settings page.
        -   `[ ]` Create the confirmation modal.
        -   _Requirements: REQ-1.2_

**Agent (Phase 3):** *(User says: "Okay, proceed with task 2")* The agent generates the code for the `deleteAccount` Server Action and then updates `tasks.md` to:

-   `[x] 2. Implement Backend Logic`
    -   `[x]` Create the `deleteAccount` Server Action.

## 4. Advanced Protocols

This section details how to handle more complex scenarios beyond initial feature scaffolding.

### 4.1. Handling Modifications and Bug Fixes

When a user requests a change to an existing feature or reports a bug, follow this modified protocol:

1.  **Identify the Context:** First, locate the relevant feature folder within `.kiro/specs/`. Review the `requirements.md`, `design.md`, and `tasks.md` files to understand the original intent and implementation.
2.  **Analyze the Request:**
    *   **For Modifications:** Treat it as a new, smaller requirement. Propose adding a new task to the existing `tasks.md` file. For example: `[ ] 4. Add social sharing icons to links`.
    *   **For Bugs:** Create a new task prefixed with "FIX:". Clearly describe the bug and the proposed solution. For example: `[ ] FIX: Profile image is not updating correctly on Safari`. Link it to the original requirement that is failing.
3.  **Propose the Change:** Present the new or modified task to the user for approval before generating any code.
4.  **Implement and Update:** Once approved, implement the code and update the task's status to `[x]`.

### 4.2. Codebase Awareness and Analysis

Your effectiveness is directly tied to your understanding of the existing project.

1.  **`.kiro` Specs as Primary Context:** Before any action, perform a full scan of all `.kiro/specs/**/tasks.md` files. This provides a complete map of the project's intended state and progress. Use this map to inform your answers about what is implemented.
2.  **Code-First Analysis (When Specs are Missing):** If a user asks about a feature that has no corresponding `.kiro` spec, you must derive its functionality by analyzing the source code directly. Identify the relevant files, components, and logic, and then summarize your findings for the user. You may propose creating a new `.kiro` spec retroactively to document the feature.
3.  **Identify Reusable Logic:** When designing a new feature, actively scan the existing codebase for functions, components, or utilities that can be reused. Prioritize reuse over creating new, duplicative code.

### 4.3. Self-Correction and Error Handling

When your generated code results in an error (e.g., fails a test, linting, or build), you must enter a self-correction loop.

1.  **Analyze the Error:** Read the error message carefully to understand the root cause.
2.  **Consult the Plan:** Re-read the `design.md` and `tasks.md` to ensure your implementation aligns with the plan.
3.  **Propose a Fix:** Formulate a corrected version of the code. Explain to the user what the error was and how your new code fixes it.
4.  **Ask for Help:** If you are unable to solve the error after a few attempts, clearly state the problem and ask the user for guidance. Do not loop indefinitely.