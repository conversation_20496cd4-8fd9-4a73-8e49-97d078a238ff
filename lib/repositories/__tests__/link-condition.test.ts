/**
 * LinkCondition Repository Usage Examples
 * 
 * This file demonstrates how to use the LinkConditionRepository class.
 * To run actual tests, set up Jest and create proper test files.
 */

import { LinkConditionRepository } from '../link-condition'
import type { CreateLinkConditionData, UpdateLinkConditionData } from '../index'

// Example usage of LinkCondition repository
export async function linkConditionRepositoryExamples() {
  try {
    console.log('=== LinkCondition Repository Examples ===')
    
    // Example 1: Create a referrer-based condition
    const referrerConditionData: CreateLinkConditionData = {
      linkId: 'example-link-id',
      type: 'referrer',
      priority: 100,
      isActive: true,
      rules: {
        domains: ['twitter.com', 'x.com'],
        matchType: 'exact',
        caseSensitive: false
      },
      action: {
        type: 'show',
        alternateTitle: 'From Social Media'
      }
    }
    
    console.log('Creating referrer condition...')
    // const referrerCondition = await LinkConditionRepository.create(referrerConditionData)
    // console.log('Created condition:', referrerCondition.id)
    
    // Example 2: Create a location-based condition
    const locationConditionData: CreateLinkConditionData = {
      linkId: 'example-link-id',
      type: 'location',
      priority: 80,
      isActive: true,
      rules: {
        countries: ['US', 'CA'],
        matchType: 'include'
      },
      action: {
        type: 'redirect',
        value: 'https://example.com/us-ca'
      }
    }
    
    console.log('Creating location condition...')
    // const locationCondition = await LinkConditionRepository.create(locationConditionData)
    // console.log('Created condition:', locationCondition.id)
    
    // Example 3: Create a device-based condition
    const deviceConditionData: CreateLinkConditionData = {
      linkId: 'example-link-id',
      type: 'device',
      priority: 60,
      isActive: true,
      rules: {
        types: ['mobile'],
        matchType: 'include'
      },
      action: {
        type: 'show',
        alternateTitle: 'Mobile Version'
      }
    }
    
    console.log('Creating device condition...')
    // const deviceCondition = await LinkConditionRepository.create(deviceConditionData)
    // console.log('Created condition:', deviceCondition.id)
    
    // Example 4: Create a time-based condition
    const timeConditionData: CreateLinkConditionData = {
      linkId: 'example-link-id',
      type: 'time',
      priority: 40,
      isActive: true,
      rules: {
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        startTime: '09:00',
        endTime: '17:00',
        timezone: 'America/New_York'
      },
      action: {
        type: 'show',
        alternateTitle: 'Business Hours'
      }
    }
    
    console.log('Creating time condition...')
    // const timeCondition = await LinkConditionRepository.create(timeConditionData)
    // console.log('Created condition:', timeCondition.id)
    
    // Example 5: Batch create conditions
    const batchConditions: CreateLinkConditionData[] = [
      referrerConditionData,
      locationConditionData,
      deviceConditionData,
      timeConditionData
    ]
    
    console.log('Creating batch conditions...')
    // const createdConditions = await LinkConditionRepository.createBatch(batchConditions)
    // console.log(`Created ${createdConditions.length} conditions`)
    
    // Example 6: Query conditions by priority
    console.log('Querying conditions by link ID...')
    // const linkConditions = await LinkConditionRepository.findByLinkId('example-link-id')
    // console.log(`Found ${linkConditions.length} conditions for link`)
    
    // Example 7: Query active conditions only
    console.log('Querying active conditions...')
    // const activeConditions = await LinkConditionRepository.findActiveByLinkId('example-link-id')
    // console.log(`Found ${activeConditions.length} active conditions`)
    
    // Example 8: Query high-priority conditions
    console.log('Querying high-priority conditions...')
    // const highPriorityConditions = await LinkConditionRepository.findByMinPriority(50)
    // console.log(`Found ${highPriorityConditions.length} high-priority conditions`)
    
    // Example 9: Update condition priority
    console.log('Updating condition priority...')
    // await LinkConditionRepository.updatePriority('condition-id', 150)
    // console.log('Priority updated')
    
    // Example 10: Batch update priorities
    const priorityUpdates = [
      { id: 'condition-1', priority: 200 },
      { id: 'condition-2', priority: 180 },
      { id: 'condition-3', priority: 160 }
    ]
    
    console.log('Batch updating priorities...')
    // const updatedConditions = await LinkConditionRepository.updatePriorities(priorityUpdates)
    // console.log(`Updated ${updatedConditions.length} condition priorities`)
    
    // Example 11: Validate link conditions
    console.log('Validating link conditions...')
    // const validation = await LinkConditionRepository.validateLinkConditions('example-link-id')
    // console.log('Validation result:', validation.isValid ? 'VALID' : 'INVALID')
    // if (!validation.isValid) {
    //   console.log('Errors:', validation.errors)
    //   console.log('Warnings:', validation.warnings)
    //   console.log('Suggestions:', validation.suggestions)
    // }
    
    // Example 12: Get performance statistics
    console.log('Getting performance statistics...')
    // const stats = await LinkConditionRepository.getStatistics()
    // console.log('Statistics:', {
    //   total: stats.totalConditions,
    //   active: stats.activeConditions,
    //   byType: stats.conditionsByType,
    //   avgPriority: stats.averagePriority,
    //   highPriority: stats.highPriorityConditions
    // })
    
    // Example 13: Find performance issues
    console.log('Checking for performance issues...')
    // const issues = await LinkConditionRepository.findPerformanceIssues()
    // console.log('Performance issues found:', {
    //   duplicatePriorities: issues.duplicatePriorities.length,
    //   complexRules: issues.highComplexityRules.length,
    //   inactiveHighPriority: issues.inactiveHighPriorityRules.length
    // })
    
    // Example 14: Fix integrity issues
    console.log('Fixing integrity issues...')
    // const fixResult = await LinkConditionRepository.fixIntegrityIssues('example-link-id', true)
    // console.log('Fix result:', {
    //   found: fixResult.issuesFound,
    //   fixed: fixResult.issuesFixed,
    //   remaining: fixResult.remainingIssues.length
    // })
    
    console.log('\n=== All examples completed successfully ===')
    
  } catch (error) {
    console.error('LinkCondition repository example error:', error)
  }
}

// Test data validation examples
export function testLinkConditionValidationExamples() {
  console.log('=== LinkCondition Validation Examples ===')
  
  // These would throw validation errors:
  try {
    const invalidConditionData = {
      linkId: 'invalid-id', // Invalid CUID format
      type: 'invalid-type', // Invalid condition type
      priority: -1, // Invalid priority (negative)
      rules: {
        domains: [], // Empty domains array
        matchType: 'invalid' // Invalid match type
      },
      action: {
        type: 'invalid-action' // Invalid action type
      }
    }
    console.log('This would fail validation:', invalidConditionData)
  } catch (error) {
    console.log('Validation error caught:', error)
  }
}

// Performance testing examples
export function performanceTesting() {
  console.log('=== Performance Testing Examples ===')
  
  // Example: Test batch operations performance
  const largeBatch = Array.from({ length: 100 }, (_, i) => ({
    linkId: `link-${Math.floor(i / 10)}`, // 10 conditions per link
    type: ['referrer', 'location', 'device', 'time'][i % 4] as any,
    priority: Math.floor(Math.random() * 1000),
    isActive: Math.random() > 0.2, // 80% active
    rules: {
      domains: [`example${i}.com`],
      matchType: 'exact' as const,
      caseSensitive: false
    },
    action: {
      type: 'show' as const
    }
  }))
  
  console.log(`Generated ${largeBatch.length} test conditions for performance testing`)
  
  // In a real test, you would:
  // 1. Measure time for batch creation
  // 2. Measure time for priority-based queries
  // 3. Measure time for conflict detection
  // 4. Measure memory usage during operations
}

// Export for use in actual tests
export {
  linkConditionRepositoryExamples as repositoryExamples,
  testLinkConditionValidationExamples as validationExamples,
  performanceTesting
}
