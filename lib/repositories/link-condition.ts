import { LinkCondition, Prisma } from '@prisma/client'
import { db, handleDatabaseError, NotFoundError, ConflictError, ValidationError, withTransaction } from '../db'
import { 
  createLinkConditionSchema, 
  updateLinkConditionSchema, 
  type CreateLinkConditionData, 
  type UpdateLinkConditionData 
} from '../validations'
import { RuleValidator } from '../rule-engine/rule-validator'
import { RuleConflictResolver } from '../rule-engine/conflict-resolver'

/**
 * Repository for managing LinkCondition entities
 * Provides CRUD operations, priority-based querying, and batch operations
 */
export class LinkConditionRepository {
  /**
   * Create a new link condition with validation and conflict detection
   */
  static async create(data: CreateLinkConditionData): Promise<LinkCondition> {
    try {
      // Validate input data
      const validatedData = createLinkConditionSchema.parse(data)
      
      // Verify the link exists
      const link = await db.link.findUnique({
        where: { id: validatedData.linkId },
        include: { conditions: true }
      })
      
      if (!link) {
        throw new NotFoundError('Link', validatedData.linkId)
      }

      // Validate rule configuration
      const ruleValidation = RuleValidator.validateRule(validatedData.rules)
      if (!ruleValidation.isValid) {
        throw new ValidationError(`Rule validation failed: ${ruleValidation.errors.join(', ')}`)
      }

      // Check for conflicts with existing conditions
      const existingConditions = link.conditions.map(condition => ({
        ...condition,
        rules: condition.rules as any,
        action: condition.action as any,
        isValid: true,
        validationErrors: [],
        performanceImpact: 'low' as const
      }))

      const newCondition = {
        id: 'temp-id',
        linkId: validatedData.linkId,
        type: validatedData.type,
        priority: validatedData.priority,
        isActive: validatedData.isActive,
        rules: validatedData.rules,
        action: validatedData.action,
        createdAt: new Date(),
        updatedAt: new Date(),
        isValid: true,
        validationErrors: [],
        performanceImpact: 'low' as const
      }

      const linkWithConditions = {
        ...link,
        conditions: [...existingConditions, newCondition],
        hasConditions: true,
        defaultBehavior: link.defaultBehavior as 'show' | 'hide'
      }

      const conflictResult = RuleConflictResolver.detectConflicts(linkWithConditions)
      if (conflictResult.hasConflicts) {
        const errorMessages = conflictResult.conflicts
          .filter(c => c.severity === 'error')
          .map(c => c.description)
        
        if (errorMessages.length > 0) {
          throw new ConflictError(`Rule conflicts detected: ${errorMessages.join('; ')}`)
        }
      }

      // Create the condition
      return await db.linkCondition.create({
        data: {
          ...validatedData,
          rules: validatedData.rules as Prisma.JsonObject,
          action: validatedData.action as Prisma.JsonObject
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find condition by ID
   */
  static async findById(id: string): Promise<LinkCondition | null> {
    try {
      return await db.linkCondition.findUnique({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find condition by ID or throw error
   */
  static async findByIdOrThrow(id: string): Promise<LinkCondition> {
    const condition = await this.findById(id)
    if (!condition) {
      throw new NotFoundError('LinkCondition', id)
    }
    return condition
  }

  /**
   * Find all conditions for a link, ordered by priority (descending) then creation date (descending)
   */
  static async findByLinkId(linkId: string): Promise<LinkCondition[]> {
    try {
      return await db.linkCondition.findMany({
        where: { linkId },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find active conditions for a link, ordered by priority
   */
  static async findActiveByLinkId(linkId: string): Promise<LinkCondition[]> {
    try {
      return await db.linkCondition.findMany({
        where: { 
          linkId,
          isActive: true
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find conditions by type
   */
  static async findByType(type: string): Promise<LinkCondition[]> {
    try {
      return await db.linkCondition.findMany({
        where: { type },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find conditions with priority greater than or equal to specified value
   */
  static async findByMinPriority(minPriority: number): Promise<LinkCondition[]> {
    try {
      return await db.linkCondition.findMany({
        where: { 
          priority: { gte: minPriority },
          isActive: true
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update a condition with validation and conflict detection
   */
  static async update(id: string, data: UpdateLinkConditionData): Promise<LinkCondition> {
    try {
      // Validate input data
      const validatedData = updateLinkConditionSchema.parse(data)
      
      // Get existing condition
      const existingCondition = await this.findByIdOrThrow(id)
      
      // If rules are being updated, validate them
      if (validatedData.rules) {
        const ruleValidation = RuleValidator.validateRule(validatedData.rules)
        if (!ruleValidation.isValid) {
          throw new ValidationError(`Rule validation failed: ${ruleValidation.errors.join(', ')}`)
        }
      }

      // Check for conflicts if priority, rules, or type are changing
      if (validatedData.priority !== undefined || validatedData.rules || validatedData.type) {
        const link = await db.link.findUnique({
          where: { id: existingCondition.linkId },
          include: { conditions: true }
        })

        if (link) {
          const updatedConditions = link.conditions.map(condition => {
            if (condition.id === id) {
              return {
                ...condition,
                ...validatedData,
                rules: (validatedData.rules || condition.rules) as any,
                action: (validatedData.action || condition.action) as any,
                isValid: true,
                validationErrors: [],
                performanceImpact: 'low' as const
              }
            }
            return {
              ...condition,
              rules: condition.rules as any,
              action: condition.action as any,
              isValid: true,
              validationErrors: [],
              performanceImpact: 'low' as const
            }
          })

          const linkWithConditions = {
            ...link,
            conditions: updatedConditions,
            hasConditions: true,
            defaultBehavior: link.defaultBehavior as 'show' | 'hide'
          }

          const conflictResult = RuleConflictResolver.detectConflicts(linkWithConditions)
          if (conflictResult.hasConflicts) {
            const errorMessages = conflictResult.conflicts
              .filter(c => c.severity === 'error')
              .map(c => c.description)
            
            if (errorMessages.length > 0) {
              throw new ConflictError(`Rule conflicts detected: ${errorMessages.join('; ')}`)
            }
          }
        }
      }

      // Update the condition
      return await db.linkCondition.update({
        where: { id },
        data: {
          ...validatedData,
          ...(validatedData.rules && { rules: validatedData.rules as Prisma.JsonObject }),
          ...(validatedData.action && { action: validatedData.action as Prisma.JsonObject })
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Delete a condition
   */
  static async delete(id: string): Promise<LinkCondition> {
    try {
      const existingCondition = await this.findByIdOrThrow(id)
      
      return await db.linkCondition.delete({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Toggle condition active status
   */
  static async toggleActive(id: string): Promise<LinkCondition> {
    try {
      const condition = await this.findByIdOrThrow(id)
      
      return await db.linkCondition.update({
        where: { id },
        data: {
          isActive: !condition.isActive
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update condition priority
   */
  static async updatePriority(id: string, priority: number): Promise<LinkCondition> {
    try {
      if (priority < 0 || priority > 1000) {
        throw new ValidationError('Priority must be between 0 and 1000')
      }

      return await db.linkCondition.update({
        where: { id },
        data: { priority }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  // ===== BATCH OPERATIONS =====

  /**
   * Create multiple conditions in a single transaction
   */
  static async createBatch(conditions: CreateLinkConditionData[]): Promise<LinkCondition[]> {
    try {
      if (conditions.length === 0) {
        return []
      }

      // Validate all conditions first
      const validatedConditions = conditions.map(data => createLinkConditionSchema.parse(data))

      // Group by linkId for conflict checking
      const conditionsByLink = validatedConditions.reduce((acc, condition) => {
        if (!acc[condition.linkId]) {
          acc[condition.linkId] = []
        }
        acc[condition.linkId].push(condition)
        return acc
      }, {} as Record<string, CreateLinkConditionData[]>)

      // Validate each link's conditions for conflicts
      for (const [linkId, linkConditions] of Object.entries(conditionsByLink)) {
        const link = await db.link.findUnique({
          where: { id: linkId },
          include: { conditions: true }
        })

        if (!link) {
          throw new NotFoundError('Link', linkId)
        }

        // Validate rules for each condition
        for (const condition of linkConditions) {
          const ruleValidation = RuleValidator.validateRule(condition.rules)
          if (!ruleValidation.isValid) {
            throw new ValidationError(`Rule validation failed for link ${linkId}: ${ruleValidation.errors.join(', ')}`)
          }
        }

        // Check for conflicts
        const existingConditions = link.conditions.map(condition => ({
          ...condition,
          rules: condition.rules as any,
          action: condition.action as any,
          isValid: true,
          validationErrors: [],
          performanceImpact: 'low' as const
        }))

        const newConditions = linkConditions.map((condition, index) => ({
          id: `temp-${index}`,
          linkId: condition.linkId,
          type: condition.type,
          priority: condition.priority,
          isActive: condition.isActive,
          rules: condition.rules,
          action: condition.action,
          createdAt: new Date(),
          updatedAt: new Date(),
          isValid: true,
          validationErrors: [],
          performanceImpact: 'low' as const
        }))

        const linkWithConditions = {
          ...link,
          conditions: [...existingConditions, ...newConditions],
          hasConditions: true,
          defaultBehavior: link.defaultBehavior as 'show' | 'hide'
        }

        const conflictResult = RuleConflictResolver.detectConflicts(linkWithConditions)
        if (conflictResult.hasConflicts) {
          const errorMessages = conflictResult.conflicts
            .filter(c => c.severity === 'error')
            .map(c => c.description)

          if (errorMessages.length > 0) {
            throw new ConflictError(`Rule conflicts detected for link ${linkId}: ${errorMessages.join('; ')}`)
          }
        }
      }

      // Create all conditions in a transaction
      return await withTransaction(async (tx) => {
        const createdConditions: LinkCondition[] = []

        for (const condition of validatedConditions) {
          const created = await tx.linkCondition.create({
            data: {
              ...condition,
              rules: condition.rules as Prisma.JsonObject,
              action: condition.action as Prisma.JsonObject
            }
          })
          createdConditions.push(created)
        }

        return createdConditions
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update multiple conditions in a single transaction
   */
  static async updateBatch(updates: Array<{ id: string; data: UpdateLinkConditionData }>): Promise<LinkCondition[]> {
    try {
      if (updates.length === 0) {
        return []
      }

      // Validate all update data first
      const validatedUpdates = updates.map(update => ({
        id: update.id,
        data: updateLinkConditionSchema.parse(update.data)
      }))

      // Get all existing conditions
      const conditionIds = validatedUpdates.map(u => u.id)
      const existingConditions = await db.linkCondition.findMany({
        where: { id: { in: conditionIds } }
      })

      if (existingConditions.length !== conditionIds.length) {
        const foundIds = existingConditions.map(c => c.id)
        const missingIds = conditionIds.filter(id => !foundIds.includes(id))
        throw new NotFoundError('LinkCondition', missingIds.join(', '))
      }

      // Validate rules for conditions that are changing type or rules
      for (const update of validatedUpdates) {
        const existingCondition = existingConditions.find(c => c.id === update.id)!

        if (update.data.rules) {
          const ruleValidation = RuleValidator.validateRule(update.data.rules)
          if (!ruleValidation.isValid) {
            throw new ValidationError(`Rule validation failed for condition ${update.id}: ${ruleValidation.errors.join(', ')}`)
          }
        }
      }

      // Update all conditions in a transaction
      return await withTransaction(async (tx) => {
        const updatedConditions: LinkCondition[] = []

        for (const update of validatedUpdates) {
          const updated = await tx.linkCondition.update({
            where: { id: update.id },
            data: {
              ...update.data,
              ...(update.data.rules && { rules: update.data.rules as Prisma.JsonObject }),
              ...(update.data.action && { action: update.data.action as Prisma.JsonObject })
            }
          })
          updatedConditions.push(updated)
        }

        return updatedConditions
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Delete multiple conditions in a single transaction
   */
  static async deleteBatch(ids: string[]): Promise<LinkCondition[]> {
    try {
      if (ids.length === 0) {
        return []
      }

      // Get all existing conditions first
      const existingConditions = await db.linkCondition.findMany({
        where: { id: { in: ids } }
      })

      if (existingConditions.length !== ids.length) {
        const foundIds = existingConditions.map(c => c.id)
        const missingIds = ids.filter(id => !foundIds.includes(id))
        throw new NotFoundError('LinkCondition', missingIds.join(', '))
      }

      // Delete all conditions in a transaction
      return await withTransaction(async (tx) => {
        const deletedConditions: LinkCondition[] = []

        for (const id of ids) {
          const deleted = await tx.linkCondition.delete({
            where: { id }
          })
          deletedConditions.push(deleted)
        }

        return deletedConditions
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Bulk update priorities for multiple conditions
   */
  static async updatePriorities(updates: Array<{ id: string; priority: number }>): Promise<LinkCondition[]> {
    try {
      if (updates.length === 0) {
        return []
      }

      // Validate priorities
      for (const update of updates) {
        if (update.priority < 0 || update.priority > 1000) {
          throw new ValidationError(`Priority must be between 0 and 1000 for condition ${update.id}`)
        }
      }

      // Get all existing conditions
      const conditionIds = updates.map(u => u.id)
      const existingConditions = await db.linkCondition.findMany({
        where: { id: { in: conditionIds } }
      })

      if (existingConditions.length !== conditionIds.length) {
        const foundIds = existingConditions.map(c => c.id)
        const missingIds = conditionIds.filter(id => !foundIds.includes(id))
        throw new NotFoundError('LinkCondition', missingIds.join(', '))
      }

      // Update all priorities in a transaction
      return await withTransaction(async (tx) => {
        const updatedConditions: LinkCondition[] = []

        for (const update of updates) {
          const updated = await tx.linkCondition.update({
            where: { id: update.id },
            data: { priority: update.priority }
          })
          updatedConditions.push(updated)
        }

        return updatedConditions
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Bulk toggle active status for multiple conditions
   */
  static async toggleActiveBatch(ids: string[]): Promise<LinkCondition[]> {
    try {
      if (ids.length === 0) {
        return []
      }

      // Get all existing conditions
      const existingConditions = await db.linkCondition.findMany({
        where: { id: { in: ids } }
      })

      if (existingConditions.length !== ids.length) {
        const foundIds = existingConditions.map(c => c.id)
        const missingIds = ids.filter(id => !foundIds.includes(id))
        throw new NotFoundError('LinkCondition', missingIds.join(', '))
      }

      // Toggle all active statuses in a transaction
      return await withTransaction(async (tx) => {
        const updatedConditions: LinkCondition[] = []

        for (const condition of existingConditions) {
          const updated = await tx.linkCondition.update({
            where: { id: condition.id },
            data: { isActive: !condition.isActive }
          })
          updatedConditions.push(updated)
        }

        return updatedConditions
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  // ===== PERFORMANCE OPTIMIZATIONS =====

  /**
   * Get conditions with optimized query for evaluation
   * Uses indexes and minimal data selection for performance
   */
  static async findForEvaluation(linkId: string): Promise<LinkCondition[]> {
    try {
      return await db.linkCondition.findMany({
        where: {
          linkId,
          isActive: true
        },
        select: {
          id: true,
          type: true,
          priority: true,
          rules: true,
          action: true,
          createdAt: true
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      }) as LinkCondition[]
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get conditions count by link
   */
  static async getConditionCounts(linkIds: string[]): Promise<Record<string, number>> {
    try {
      if (linkIds.length === 0) {
        return {}
      }

      const counts = await db.linkCondition.groupBy({
        by: ['linkId'],
        where: {
          linkId: { in: linkIds },
          isActive: true
        },
        _count: {
          id: true
        }
      })

      return counts.reduce((acc, count) => {
        acc[count.linkId] = count._count.id
        return acc
      }, {} as Record<string, number>)
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get conditions statistics for performance monitoring
   */
  static async getStatistics(): Promise<{
    totalConditions: number
    activeConditions: number
    conditionsByType: Record<string, number>
    averagePriority: number
    highPriorityConditions: number
  }> {
    try {
      const [
        totalCount,
        activeCount,
        typeStats,
        priorityStats
      ] = await Promise.all([
        db.linkCondition.count(),
        db.linkCondition.count({ where: { isActive: true } }),
        db.linkCondition.groupBy({
          by: ['type'],
          _count: { id: true }
        }),
        db.linkCondition.aggregate({
          _avg: { priority: true },
          _count: {
            priority: {
              where: { priority: { gte: 100 } }
            }
          }
        })
      ])

      const conditionsByType = typeStats.reduce((acc, stat) => {
        acc[stat.type] = stat._count.id
        return acc
      }, {} as Record<string, number>)

      return {
        totalConditions: totalCount,
        activeConditions: activeCount,
        conditionsByType,
        averagePriority: priorityStats._avg.priority || 0,
        highPriorityConditions: priorityStats._count.priority
      }
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find conditions that may have performance issues
   */
  static async findPerformanceIssues(): Promise<{
    duplicatePriorities: Array<{ linkId: string; priority: number; count: number }>
    highComplexityRules: LinkCondition[]
    inactiveHighPriorityRules: LinkCondition[]
  }> {
    try {
      const [duplicatePriorities, highComplexityRules, inactiveHighPriorityRules] = await Promise.all([
        // Find duplicate priorities within the same link
        db.$queryRaw<Array<{ linkId: string; priority: number; count: bigint }>>`
          SELECT "linkId", "priority", COUNT(*) as count
          FROM "LinkCondition"
          WHERE "isActive" = true
          GROUP BY "linkId", "priority"
          HAVING COUNT(*) > 1
        `,

        // Find rules with complex regex patterns (potential performance issue)
        db.linkCondition.findMany({
          where: {
            isActive: true,
            type: 'referrer',
            // This is a simplified check - in practice you'd analyze the JSON rules
          },
          take: 100
        }),

        // Find inactive rules with high priority (potential configuration issue)
        db.linkCondition.findMany({
          where: {
            isActive: false,
            priority: { gte: 100 }
          },
          take: 100
        })
      ])

      return {
        duplicatePriorities: duplicatePriorities.map(dp => ({
          linkId: dp.linkId,
          priority: dp.priority,
          count: Number(dp.count)
        })),
        highComplexityRules,
        inactiveHighPriorityRules
      }
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  // ===== CACHING INTEGRATION =====

  /**
   * Cache key generator for conditions
   */
  private static getCacheKey(linkId: string, suffix?: string): string {
    return `link_conditions:${linkId}${suffix ? `:${suffix}` : ''}`
  }

  /**
   * Get conditions with caching support
   * Note: This assumes a caching layer is available (Redis, in-memory, etc.)
   */
  static async findByLinkIdCached(linkId: string, ttl: number = 300): Promise<LinkCondition[]> {
    try {
      const cacheKey = this.getCacheKey(linkId, 'all')

      // Try to get from cache first (implementation depends on your caching solution)
      // For now, we'll just call the regular method
      // In a real implementation, you'd check cache here

      const conditions = await this.findByLinkId(linkId)

      // Cache the result (implementation depends on your caching solution)
      // In a real implementation, you'd cache the result here

      return conditions
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get active conditions with caching support
   */
  static async findActiveByLinkIdCached(linkId: string, ttl: number = 300): Promise<LinkCondition[]> {
    try {
      const cacheKey = this.getCacheKey(linkId, 'active')

      // Try to get from cache first (implementation depends on your caching solution)
      // For now, we'll just call the regular method

      const conditions = await this.findActiveByLinkId(linkId)

      // Cache the result (implementation depends on your caching solution)

      return conditions
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Invalidate cache for a link's conditions
   */
  static async invalidateCache(linkId: string): Promise<void> {
    try {
      // Implementation depends on your caching solution
      // You would invalidate all cache keys related to this link
      const cacheKeys = [
        this.getCacheKey(linkId, 'all'),
        this.getCacheKey(linkId, 'active'),
        this.getCacheKey(linkId, 'evaluation')
      ]

      // In a real implementation, you'd invalidate these cache keys
      console.log(`Would invalidate cache keys: ${cacheKeys.join(', ')}`)
    } catch (error) {
      console.error('Error invalidating cache:', error)
    }
  }

  // ===== DATA INTEGRITY CHECKS =====

  /**
   * Validate all conditions for a link
   */
  static async validateLinkConditions(linkId: string): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    suggestions: string[]
  }> {
    try {
      const link = await db.link.findUnique({
        where: { id: linkId },
        include: { conditions: true }
      })

      if (!link) {
        throw new NotFoundError('Link', linkId)
      }

      const errors: string[] = []
      const warnings: string[] = []
      const suggestions: string[] = []

      // Validate each condition's rules
      for (const condition of link.conditions) {
        const ruleValidation = RuleValidator.validateRule(condition.rules)
        if (!ruleValidation.isValid) {
          errors.push(`Condition ${condition.id}: ${ruleValidation.errors.join(', ')}`)
        }
        warnings.push(...ruleValidation.warnings.map(w => `Condition ${condition.id}: ${w}`))
      }

      // Check for conflicts
      const enhancedConditions = link.conditions.map(condition => ({
        ...condition,
        rules: condition.rules as any,
        action: condition.action as any,
        isValid: true,
        validationErrors: [],
        performanceImpact: 'low' as const
      }))

      const linkWithConditions = {
        ...link,
        conditions: enhancedConditions,
        hasConditions: link.conditions.length > 0,
        defaultBehavior: link.defaultBehavior as 'show' | 'hide'
      }

      const conflictResult = RuleConflictResolver.detectConflicts(linkWithConditions)
      if (conflictResult.hasConflicts) {
        errors.push(...conflictResult.conflicts
          .filter(c => c.severity === 'error')
          .map(c => c.description))
        warnings.push(...conflictResult.conflicts
          .filter(c => c.severity === 'warning')
          .map(c => c.description))
      }
      suggestions.push(...conflictResult.suggestions)

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions
      }
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Fix common data integrity issues
   */
  static async fixIntegrityIssues(linkId: string, autoFix: boolean = false): Promise<{
    issuesFound: number
    issuesFixed: number
    remainingIssues: string[]
  }> {
    try {
      const validation = await this.validateLinkConditions(linkId)
      let issuesFixed = 0
      const remainingIssues: string[] = []

      if (autoFix) {
        // Auto-fix simple issues like duplicate priorities
        const performanceIssues = await this.findPerformanceIssues()
        const linkDuplicates = performanceIssues.duplicatePriorities.filter(dp => dp.linkId === linkId)

        for (const duplicate of linkDuplicates) {
          // Reassign priorities to resolve duplicates
          const conditions = await this.findByLinkId(linkId)
          const duplicateConditions = conditions.filter(c => c.priority === duplicate.priority)

          for (let i = 0; i < duplicateConditions.length; i++) {
            if (i > 0) { // Keep the first one, adjust others
              await this.updatePriority(duplicateConditions[i].id, duplicate.priority + i)
              issuesFixed++
            }
          }
        }
      }

      // Collect remaining issues that couldn't be auto-fixed
      remainingIssues.push(...validation.errors)

      return {
        issuesFound: validation.errors.length + validation.warnings.length,
        issuesFixed,
        remainingIssues
      }
    } catch (error) {
      return handleDatabaseError(error)
    }
  }
}
