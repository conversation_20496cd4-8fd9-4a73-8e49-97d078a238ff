# LinkCondition Repository

The LinkCondition Repository provides comprehensive CRUD operations, priority-based querying, batch operations, and performance optimizations for managing conditional link rules.

## Features

### ✅ Core CRUD Operations
- Create, read, update, delete individual conditions
- Find conditions by ID, link ID, type, and priority
- Toggle active status and update priorities

### ✅ Priority-Based Querying
- Automatic ordering by priority (descending) then creation date (descending)
- Query conditions with minimum priority threshold
- Optimized queries for rule evaluation

### ✅ Batch Operations
- Create multiple conditions in a single transaction
- Update multiple conditions with validation
- Delete multiple conditions safely
- Bulk priority updates and status toggles

### ✅ Rule Validation Integration
- Validates rule syntax and semantics before saving
- Prevents invalid rule configurations
- Integrates with RuleValidator for comprehensive validation

### ✅ Conflict Detection
- Detects rule conflicts before saving
- Prevents contradictory and unreachable rules
- Integrates with RuleConflictResolver

### ✅ Data Integrity Checks
- Validates all conditions for a link
- Identifies and fixes common integrity issues
- Provides detailed validation reports

### ✅ Performance Optimizations
- Optimized queries with proper indexing
- Minimal data selection for evaluation queries
- Performance statistics and monitoring
- Identifies potential performance issues

### ✅ Caching Integration
- Cache-aware methods for frequently accessed data
- Cache invalidation on updates
- Configurable TTL for cached results

## Usage Examples

### Basic CRUD Operations

```typescript
import { LinkConditionRepository } from '@/lib/repositories'

// Create a condition
const condition = await LinkConditionRepository.create({
  linkId: 'link-123',
  type: 'referrer',
  priority: 100,
  isActive: true,
  rules: {
    domains: ['twitter.com', 'x.com'],
    matchType: 'exact',
    caseSensitive: false
  },
  action: {
    type: 'show',
    alternateTitle: 'From Social Media'
  }
})

// Find conditions for a link (ordered by priority)
const conditions = await LinkConditionRepository.findByLinkId('link-123')

// Find only active conditions
const activeConditions = await LinkConditionRepository.findActiveByLinkId('link-123')

// Update a condition
const updated = await LinkConditionRepository.update('condition-123', {
  priority: 150,
  isActive: false
})

// Delete a condition
await LinkConditionRepository.delete('condition-123')
```

### Batch Operations

```typescript
// Create multiple conditions
const conditions = await LinkConditionRepository.createBatch([
  { linkId: 'link-1', type: 'referrer', priority: 100, /* ... */ },
  { linkId: 'link-1', type: 'location', priority: 80, /* ... */ },
  { linkId: 'link-2', type: 'device', priority: 60, /* ... */ }
])

// Update multiple priorities
const updated = await LinkConditionRepository.updatePriorities([
  { id: 'condition-1', priority: 200 },
  { id: 'condition-2', priority: 180 }
])

// Toggle multiple conditions
const toggled = await LinkConditionRepository.toggleActiveBatch([
  'condition-1', 'condition-2', 'condition-3'
])
```

### Performance Monitoring

```typescript
// Get performance statistics
const stats = await LinkConditionRepository.getStatistics()
console.log({
  total: stats.totalConditions,
  active: stats.activeConditions,
  byType: stats.conditionsByType,
  avgPriority: stats.averagePriority
})

// Find performance issues
const issues = await LinkConditionRepository.findPerformanceIssues()
console.log({
  duplicatePriorities: issues.duplicatePriorities.length,
  complexRules: issues.highComplexityRules.length
})

// Get optimized conditions for evaluation
const evalConditions = await LinkConditionRepository.findForEvaluation('link-123')
```

### Validation and Integrity

```typescript
// Validate all conditions for a link
const validation = await LinkConditionRepository.validateLinkConditions('link-123')
if (!validation.isValid) {
  console.log('Errors:', validation.errors)
  console.log('Warnings:', validation.warnings)
  console.log('Suggestions:', validation.suggestions)
}

// Fix integrity issues automatically
const fixResult = await LinkConditionRepository.fixIntegrityIssues('link-123', true)
console.log(`Fixed ${fixResult.issuesFixed} out of ${fixResult.issuesFound} issues`)
```

## Validation Schemas

The repository uses comprehensive Zod schemas for validation:

### Condition Types
- `referrer`: Domain-based rules with match types (exact, contains, regex)
- `location`: Country/region/city-based rules with include/exclude logic
- `device`: Device type/platform/browser-based rules
- `time`: Day of week and time range-based rules

### Action Types
- `show`: Display the link (with optional alternate title/icon)
- `hide`: Hide the link
- `redirect`: Redirect to alternate URL

### Priority System
- Priority range: 0-1000 (higher numbers = higher priority)
- Equal priorities resolved by creation date (newer first)
- Automatic conflict detection for unreachable rules

## Performance Considerations

### Indexing
The repository leverages database indexes for optimal performance:
- `(linkId, priority DESC, isActive)` - Primary evaluation query
- `(priority DESC, createdAt DESC)` - Global priority ordering
- `(type, isActive)` - Type-based queries

### Caching
- Cached queries for frequently accessed conditions
- Automatic cache invalidation on updates
- Configurable TTL for different query types

### Batch Operations
- All batch operations use database transactions
- Validation performed before database operations
- Optimized for high-throughput scenarios

## Error Handling

The repository provides comprehensive error handling:
- `ValidationError`: Invalid rule configurations or data
- `ConflictError`: Rule conflicts detected
- `NotFoundError`: Condition or link not found
- `DatabaseError`: Database operation failures

All errors include detailed context for debugging and user feedback.

## Integration

The LinkCondition Repository integrates with:
- **RuleValidator**: Syntax and semantic validation
- **RuleConflictResolver**: Conflict detection and resolution
- **Database Layer**: Optimized Prisma queries with error handling
- **Caching Layer**: Performance optimization (implementation-dependent)

## Testing

See `__tests__/link-condition.test.ts` for comprehensive usage examples and test patterns.

## Requirements Fulfilled

This implementation fulfills all requirements from the Kiro specification:
- ✅ REQ-1.1: Priority-based rule evaluation
- ✅ REQ-2.1: Conflict detection and prevention
- ✅ REQ-6.2: Data integrity validation and checks
