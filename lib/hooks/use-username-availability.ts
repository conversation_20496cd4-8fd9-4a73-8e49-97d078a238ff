'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { 
  USERNAME_ERROR_CODES, 
  UsernameErrorCode, 
  getUserFriendlyMessage,
  getErrorCodeFromMessage,
  isRetryableError 
} from '@/lib/constants/username-errors'
import { performanceMonitor } from '@/lib/utils/performance-monitor'

interface UsernameStatus {
  checking: boolean
  available?: boolean
  error?: string
  errorCode?: UsernameErrorCode
  cached?: boolean
  retryCount?: number
}

interface CacheEntry {
  available: boolean
  timestamp: number
  ttl: number
}

interface UsernameCache {
  [username: string]: CacheEntry
}

// Global cache shared across hook instances
const usernameCache: UsernameCache = {}
const CACHE_TTL = 30 * 1000 // 30 seconds
const REQUEST_TIMEOUT = 5 * 1000 // 5 seconds
const MAX_CACHE_SIZE = 100 // Prevent memory leaks

// Export cache for testing
export const __testExports = {
  usernameCache,
  clearCache: () => {
    Object.keys(usernameCache).forEach(key => delete usernameCache[key])
  }
}

// Cache management utilities
const cleanExpiredCache = () => {
  const now = Date.now()
  const entries = Object.entries(usernameCache)
  
  // Remove expired entries
  entries.forEach(([username, entry]) => {
    if (now - entry.timestamp > entry.ttl) {
      delete usernameCache[username]
    }
  })
  
  // If still too large, remove oldest entries
  const remainingEntries = Object.entries(usernameCache)
  if (remainingEntries.length > MAX_CACHE_SIZE) {
    const sortedEntries = remainingEntries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    const entriesToRemove = sortedEntries.slice(0, remainingEntries.length - MAX_CACHE_SIZE)
    entriesToRemove.forEach(([username]) => {
      delete usernameCache[username]
    })
  }
}

const getCachedResult = (username: string): CacheEntry | null => {
  const entry = usernameCache[username.toLowerCase()]
  if (!entry) {
    performanceMonitor.recordCache('miss', username.toLowerCase())
    return null
  }
  
  const now = Date.now()
  if (now - entry.timestamp > entry.ttl) {
    delete usernameCache[username.toLowerCase()]
    performanceMonitor.recordCache('miss', username.toLowerCase(), { reason: 'expired' })
    return null
  }
  
  performanceMonitor.recordCache('hit', username.toLowerCase())
  return entry
}

const setCachedResult = (username: string, available: boolean) => {
  cleanExpiredCache()
  usernameCache[username.toLowerCase()] = {
    available,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  }
  performanceMonitor.recordCache('set', username.toLowerCase(), { available })
}

export function useUsernameAvailability(currentUsername: string = '', debounceMs: number = 300) {
  const [status, setStatus] = useState<UsernameStatus>({ checking: false, num: 0 })
  const debounceTimeoutRef = useRef<NodeJS.Timeout>()
  const abortControllerRef = useRef<AbortController>()
  const retryCountRef = useRef<number>(0)
  const lastUsernameRef = useRef<string>('')

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  const checkAvailabilityInternal = useCallback(async (username: string, isRetry: boolean = false) => {
    const startTime = Date.now()
    
    // Skip check if username matches current username
    if (username === currentUsername) {
      setStatus({ checking: false, available: true })
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        true,
        undefined,
        { username, reason: 'current-username', cached: false }
      )
      return
    }
    
    // Basic format validation
    if (username.length < 3) {
      const errorCode = USERNAME_ERROR_CODES.TOO_SHORT
      setStatus({ 
        checking: false,
        error: getUserFriendlyMessage(errorCode),
        errorCode,
        retryCount: retryCountRef.current
      })
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        false,
        getUserFriendlyMessage(errorCode),
        { username, reason: 'validation-error', errorCode }
      )
      performanceMonitor.recordError(
        'username-availability-check',
        getUserFriendlyMessage(errorCode),
        errorCode,
        { username, validationType: 'length' }
      )
      return
    }
    
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      const errorCode = USERNAME_ERROR_CODES.INVALID_CHARACTERS
      setStatus({ 
        checking: false, 
        error: getUserFriendlyMessage(errorCode),
        errorCode,
        retryCount: retryCountRef.current
      })
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        false,
        getUserFriendlyMessage(errorCode),
        { username, reason: 'validation-error', errorCode }
      )
      performanceMonitor.recordError(
        'username-availability-check',
        getUserFriendlyMessage(errorCode),
        errorCode,
        { username, validationType: 'characters' }
      )
      return
    }

    // Check cache first
    const cachedResult = getCachedResult(username)
    if (cachedResult && !isRetry) {
      setStatus({ 
        checking: false, 
        available: cachedResult.available,
        error: cachedResult.available ? undefined : getUserFriendlyMessage(USERNAME_ERROR_CODES.USERNAME_TAKEN),
        errorCode: cachedResult.available ? undefined : USERNAME_ERROR_CODES.USERNAME_TAKEN,
        cached: true,
        retryCount: retryCountRef.current
      })
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        true,
        undefined,
        { username, cached: true, available: cachedResult.available }
      )
      return
    }
    
    setStatus({ checking: true, retryCount: retryCountRef.current })
    
    // Cancel previous request if exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    // Create new abort controller for this request
    abortControllerRef.current = new AbortController()
    const timeoutId = setTimeout(() => {
      abortControllerRef.current?.abort()
    }, REQUEST_TIMEOUT)
    
    try {
      const response = await fetch(
        `/api/account/username/availability?username=${encodeURIComponent(username)}`,
        {
          signal: abortControllerRef.current.signal,
          headers: {
            'Cache-Control': 'no-cache'
          }
        }
      )
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        let errorCode: UsernameErrorCode
        
        // Map HTTP status codes to error codes
        switch (response.status) {
          case 429:
            errorCode = USERNAME_ERROR_CODES.RATE_LIMITED
            break
          case 408:
            errorCode = USERNAME_ERROR_CODES.TIMEOUT_ERROR
            break
          case 500:
          case 502:
          case 503:
          case 504:
            errorCode = USERNAME_ERROR_CODES.SERVER_ERROR
            break
          default:
            errorCode = getErrorCodeFromMessage(errorData.error || '')
        }
        
        throw new Error(getUserFriendlyMessage(errorCode))
      }
      
      const result = await response.json()
      
      // Cache the result
      setCachedResult(username, result.available)
      
      setStatus({ 
        checking: false, 
        available: result.available,
        error: result.available ? undefined : getUserFriendlyMessage(USERNAME_ERROR_CODES.USERNAME_TAKEN),
        errorCode: result.available ? undefined : USERNAME_ERROR_CODES.USERNAME_TAKEN,
        cached: false,
        retryCount: retryCountRef.current
      })
      
      // Record successful performance metric
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        true,
        undefined,
        { username, cached: false, available: result.available, isRetry }
      )
      
      // Reset retry count on success
      retryCountRef.current = 0
      
    } catch (error: any) {
      clearTimeout(timeoutId)
      
      let errorCode: UsernameErrorCode
      let errorMessage: string
      
      if (error.name === 'AbortError') {
        errorCode = USERNAME_ERROR_CODES.TIMEOUT_ERROR
        errorMessage = getUserFriendlyMessage(errorCode)
      } else {
        const isNetworkError = error.message.includes('fetch') || 
                              error.message.includes('network') ||
                              error.message.includes('Failed to fetch')
        
        if (isNetworkError) {
          errorCode = USERNAME_ERROR_CODES.NETWORK_ERROR
          errorMessage = getUserFriendlyMessage(errorCode)
        } else {
          errorCode = getErrorCodeFromMessage(error.message)
          errorMessage = error.message || getUserFriendlyMessage(USERNAME_ERROR_CODES.UNKNOWN_ERROR)
        }
      }
      
      setStatus({ 
        checking: false, 
        error: errorMessage,
        errorCode,
        retryCount: retryCountRef.current
      })
      
      // Record error performance metric
      performanceMonitor.recordPerformance(
        'username-availability-check',
        Date.now() - startTime,
        false,
        errorMessage,
        { username, errorCode, isRetry, retryCount: retryCountRef.current }
      )
      
      // Record error metric
      performanceMonitor.recordError(
        'username-availability-check',
        errorMessage,
        errorCode,
        { username, isRetry, retryCount: retryCountRef.current }
      )
    }
  }, [currentUsername])

  const retry = useCallback(() => {
    const username = lastUsernameRef.current
    if (username) {
      retryCountRef.current += 1
      
      // Exponential backoff: 1s, 2s, 4s, 8s, max 8s
      const delay = Math.min(1000 * Math.pow(2, retryCountRef.current - 1), 8000)
      
      setTimeout(() => {
        checkAvailabilityInternal(username, true)
      }, delay)
    }
  }, [checkAvailabilityInternal])

  const checkAvailability = useCallback((username: string) => {
    // Clear previous debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    
    // Store the username for retry functionality
    lastUsernameRef.current = username
    
    // Reset retry count for new username
    retryCountRef.current = 0
    
    // Set up debounced check
    debounceTimeoutRef.current = setTimeout(() => {
      checkAvailabilityInternal(username)
    }, debounceMs)
  }, [checkAvailabilityInternal, debounceMs])

  const reset = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setStatus({ checking: false,  num:1 })
    retryCountRef.current = 0
    lastUsernameRef.current = ''
  }, [])

  const canRetry = status.errorCode ? isRetryableError(status.errorCode) : false
  const maxRetries = 3

  return {
    status,
    checkAvailability,
    reset,
    retry,
    canRetry: canRetry && (status.retryCount || 0) < maxRetries,
    maxRetries,
    isRetryableError: canRetry
  }
}