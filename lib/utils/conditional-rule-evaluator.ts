import { ConditionalVisitorContext } from './conditional-visitor-context'
import { isLinkScheduleActive } from './link-scheduling'

/**
 * Rule evaluation engine for conditional links
 * Evaluates visitor context against link conditions to determine visibility
 */

/**
 * Link condition types
 */
export type ConditionType = 'referrer' | 'location' | 'device' | 'time' | 'schedule'

/**
 * Condition action when rule matches
 */
export interface ConditionAction {
  type: 'show' | 'hide' | 'redirect'
  value?: string // For redirect actions
  alternateTitle?: string
  alternateIcon?: string
}

/**
 * Referrer-based rule configuration
 */
export interface ReferrerRule {
  domains: string[]
  matchType: 'exact' | 'contains' | 'regex'
  caseSensitive: boolean
}

/**
 * Location-based rule configuration
 */
export interface LocationRule {
  countries?: string[]
  regions?: string[]
  cities?: string[]
  excludeCountries?: string[]
}

/**
 * Device-based rule configuration
 */
export interface DeviceRule {
  types?: ('mobile' | 'tablet' | 'desktop')[]
  platforms?: ('ios' | 'android' | 'windows' | 'macos' | 'linux')[]
  browsers?: ('chrome' | 'firefox' | 'safari' | 'edge')[]
}

/**
 * Time-based rule configuration
 */
export interface TimeRule {
  daysOfWeek: number[] // 0-6, Sunday = 0
  timeRanges: TimeRange[]
  timezone: string
}

export interface TimeRange {
  start: string // HH:mm format
  end: string   // HH:mm format
}

/**
 * Link condition model (matches database schema)
 */
export interface LinkCondition {
  id: string
  linkId: string
  type: ConditionType
  priority: number
  isActive: boolean
  rules: ReferrerRule | LocationRule | DeviceRule | TimeRule
  action: ConditionAction
}

/**
 * Link model with conditional fields (matches database schema)
 */
export interface ConditionalLink {
  id: string
  title: string
  url: string
  icon?: string
  isVisible: boolean
  order: number
  
  // Scheduling fields
  isScheduled: boolean
  scheduleStart?: Date
  scheduleEnd?: Date
  timezone?: string
  
  // Conditional rules
  hasConditions: boolean
  conditions: LinkCondition[]
  defaultBehavior: 'show' | 'hide'
}

/**
 * Rule evaluation result
 */
export interface RuleEvaluationResult {
  linkId: string
  shouldShow: boolean
  matchedConditions: string[]
  appliedAction: ConditionAction
  evaluationTime: number
  effectiveTitle?: string
  effectiveIcon?: string
  effectiveUrl?: string
}

/**
 * Rule evaluator class
 */
export class ConditionalRuleEvaluator {
  constructor(
    private visitorContext: ConditionalVisitorContext,
    private currentTime: Date = new Date()
  ) {}

  /**
   * Evaluate a single link against all its conditions
   */
  async evaluateLink(link: ConditionalLink): Promise<RuleEvaluationResult> {
    const startTime = performance.now()
    
    // Check scheduling first (highest priority)
    if (link.isScheduled) {
      const scheduleActive = isLinkScheduleActive(link, this.currentTime)
      if (!scheduleActive) {
        return {
          linkId: link.id,
          shouldShow: false,
          matchedConditions: ['schedule'],
          appliedAction: { type: 'hide' },
          evaluationTime: performance.now() - startTime,
          effectiveTitle: link.title,
          effectiveIcon: link.icon,
          effectiveUrl: link.url
        }
      }
    }
    
    // Evaluate conditional rules by priority
    if (link.hasConditions && link.conditions.length > 0) {
      const sortedConditions = link.conditions
        .filter(c => c.isActive)
        .sort((a, b) => b.priority - a.priority)
      
      for (const condition of sortedConditions) {
        const conditionResult = this.evaluateCondition(condition)
        if (conditionResult.matches) {
          return {
            linkId: link.id,
            shouldShow: conditionResult.action.type === 'show',
            matchedConditions: [condition.id],
            appliedAction: conditionResult.action,
            evaluationTime: performance.now() - startTime,
            effectiveTitle: conditionResult.action.alternateTitle || link.title,
            effectiveIcon: conditionResult.action.alternateIcon || link.icon,
            effectiveUrl: conditionResult.action.type === 'redirect' ? conditionResult.action.value : link.url
          }
        }
      }
    }
    
    // Apply default behavior
    const shouldShow = link.defaultBehavior === 'show' && link.isVisible
    return {
      linkId: link.id,
      shouldShow,
      matchedConditions: [],
      appliedAction: { type: shouldShow ? 'show' : 'hide' },
      evaluationTime: performance.now() - startTime,
      effectiveTitle: link.title,
      effectiveIcon: link.icon,
      effectiveUrl: link.url
    }
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    switch (condition.type) {
      case 'referrer':
        return this.evaluateReferrerCondition(condition)
      case 'location':
        return this.evaluateLocationCondition(condition)
      case 'device':
        return this.evaluateDeviceCondition(condition)
      case 'time':
        return this.evaluateTimeCondition(condition)
      default:
        return { matches: false, action: { type: 'hide' } }
    }
  }

  /**
   * Evaluate referrer-based condition
   */
  private evaluateReferrerCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as ReferrerRule
    const referrer = this.visitorContext.referrer.domain
    
    if (!referrer) {
      return { matches: false, action: condition.action }
    }
    
    const matches = rules.domains.some(domain => {
      switch (rules.matchType) {
        case 'exact':
          return rules.caseSensitive 
            ? referrer === domain
            : referrer.toLowerCase() === domain.toLowerCase()
        case 'contains':
          return rules.caseSensitive
            ? referrer.includes(domain)
            : referrer.toLowerCase().includes(domain.toLowerCase())
        case 'regex':
          try {
            const flags = rules.caseSensitive ? 'g' : 'gi'
            return new RegExp(domain, flags).test(referrer)
          } catch (error) {
            console.error('Invalid regex in referrer rule:', domain, error)
            return false
          }
        default:
          return false
      }
    })
    
    return {
      matches,
      action: condition.action
    }
  }

  /**
   * Evaluate location-based condition
   */
  private evaluateLocationCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as LocationRule
    const { country, region, city } = this.visitorContext.location
    
    // Check exclusions first
    if (rules.excludeCountries && country && rules.excludeCountries.includes(country)) {
      return { matches: false, action: condition.action }
    }
    
    // Check inclusions
    let matches = false
    
    if (rules.countries && country) {
      matches = rules.countries.includes(country)
    }
    
    if (!matches && rules.regions && region) {
      matches = rules.regions.includes(region)
    }
    
    if (!matches && rules.cities && city) {
      matches = rules.cities.includes(city)
    }
    
    return {
      matches,
      action: condition.action
    }
  }

  /**
   * Evaluate device-based condition
   */
  private evaluateDeviceCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as DeviceRule
    const device = this.visitorContext.device
    
    let matches = false
    
    // Check device type
    if (rules.types && rules.types.includes(device.type)) {
      matches = true
    }
    
    // Check platform
    if (!matches && rules.platforms && rules.platforms.includes(device.platform)) {
      matches = true
    }
    
    // Check browser
    if (!matches && rules.browsers && rules.browsers.includes(device.browser)) {
      matches = true
    }
    
    return {
      matches,
      action: condition.action
    }
  }

  /**
   * Evaluate time-based condition
   */
  private evaluateTimeCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as TimeRule
    
    // Convert current time to the rule's timezone
    const now = this.convertToTimezone(this.currentTime, rules.timezone)
    
    const dayOfWeek = now.getDay()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    // Check day of week
    if (!rules.daysOfWeek.includes(dayOfWeek)) {
      return { matches: false, action: condition.action }
    }
    
    // Check time ranges
    const timeMatches = rules.timeRanges.some(range => {
      return currentTime >= range.start && currentTime <= range.end
    })
    
    return {
      matches: timeMatches,
      action: condition.action
    }
  }

  /**
   * Convert date to specific timezone (simplified implementation)
   * In production, you might want to use a library like date-fns-tz
   */
  private convertToTimezone(date: Date, timezone: string): Date {
    try {
      // This is a simplified approach using Intl.DateTimeFormat
      // For production, consider using a more robust timezone library
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
      
      const parts = formatter.formatToParts(date)
      const year = parseInt(parts.find(p => p.type === 'year')?.value || '0')
      const month = parseInt(parts.find(p => p.type === 'month')?.value || '0') - 1
      const day = parseInt(parts.find(p => p.type === 'day')?.value || '0')
      const hour = parseInt(parts.find(p => p.type === 'hour')?.value || '0')
      const minute = parseInt(parts.find(p => p.type === 'minute')?.value || '0')
      const second = parseInt(parts.find(p => p.type === 'second')?.value || '0')
      
      return new Date(year, month, day, hour, minute, second)
    } catch (error) {
      console.error('Error converting timezone:', error)
      return date // Fallback to original date
    }
  }
}

/**
 * Convenience function to evaluate multiple links
 */
export async function evaluateConditionalLinks(
  links: ConditionalLink[],
  visitorContext: ConditionalVisitorContext,
  currentTime?: Date
): Promise<RuleEvaluationResult[]> {
  const evaluator = new ConditionalRuleEvaluator(visitorContext, currentTime)
  
  const results = await Promise.all(
    links.map(link => evaluator.evaluateLink(link))
  )
  
  return results
}
