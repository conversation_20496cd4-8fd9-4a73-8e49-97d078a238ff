import { headers } from 'next/headers'
import { createConditionalVisitorContext } from './conditional-visitor-context'
import { ConditionalRuleEvaluator, ConditionalLink, RuleEvaluationResult } from './conditional-rule-evaluator'

/**
 * Server-side conditional link rendering utilities
 * Integrates visitor context detection with rule evaluation for optimal performance
 */

/**
 * Rendered link with conditional evaluation applied
 */
export interface RenderedConditionalLink {
  id: string
  title: string
  url: string
  icon?: string
  isVisible: boolean
  order: number
  clickCount: number
  
  // Conditional evaluation results
  shouldShow: boolean
  matchedConditions: string[]
  evaluationTime: number
  
  // Effective content (after conditions applied)
  effectiveTitle: string
  effectiveUrl: string
  effectiveIcon?: string
}

/**
 * Profile rendering context with visitor information
 */
export interface ProfileRenderingContext {
  userAgent: string
  referrer?: string
  currentDomain: string
  timestamp: Date
  visitorContext: ReturnType<typeof createConditionalVisitorContext>
}

/**
 * Create profile rendering context from Next.js headers
 */
export async function createProfileRenderingContext(
  currentDomain?: string
): Promise<ProfileRenderingContext> {
  const headersList = headers()
  const userAgent = headersList.get('user-agent') || ''
  const referrer = headersList.get('referer') || undefined
  const host = headersList.get('host')
  const domain = currentDomain || host?.split(':')[0] || 'localhost'
  
  const visitorContext = createConditionalVisitorContext(
    userAgent,
    headersList,
    referrer,
    domain
  )
  
  return {
    userAgent,
    referrer,
    currentDomain: domain,
    timestamp: new Date(),
    visitorContext
  }
}

/**
 * Render conditional links with visitor context evaluation
 */
export async function renderConditionalLinks(
  links: ConditionalLink[],
  renderingContext?: ProfileRenderingContext
): Promise<RenderedConditionalLink[]> {
  // Create rendering context if not provided
  const context = renderingContext || await createProfileRenderingContext()
  
  // Create rule evaluator
  const evaluator = new ConditionalRuleEvaluator(
    context.visitorContext,
    context.timestamp
  )
  
  // Evaluate all links
  const evaluationResults = await Promise.all(
    links.map(link => evaluator.evaluateLink(link))
  )
  
  // Combine link data with evaluation results
  const renderedLinks: RenderedConditionalLink[] = links.map((link, index) => {
    const evaluation = evaluationResults[index]
    
    return {
      id: link.id,
      title: link.title,
      url: link.url,
      icon: link.icon,
      isVisible: link.isVisible,
      order: link.order,
      clickCount: 0, // This would come from the database
      
      // Evaluation results
      shouldShow: evaluation.shouldShow,
      matchedConditions: evaluation.matchedConditions,
      evaluationTime: evaluation.evaluationTime,
      
      // Effective content
      effectiveTitle: evaluation.effectiveTitle || link.title,
      effectiveUrl: evaluation.effectiveUrl || link.url,
      effectiveIcon: evaluation.effectiveIcon || link.icon
    }
  })
  
  // Filter to only visible links and sort by order
  return renderedLinks
    .filter(link => link.shouldShow)
    .sort((a, b) => a.order - b.order)
}

/**
 * Render a single conditional link
 */
export async function renderSingleConditionalLink(
  link: ConditionalLink,
  renderingContext?: ProfileRenderingContext
): Promise<RenderedConditionalLink | null> {
  const results = await renderConditionalLinks([link], renderingContext)
  return results[0] || null
}

/**
 * Get visitor context for analytics tracking
 */
export async function getVisitorContextForAnalytics(
  currentDomain?: string
): Promise<{
  visitorContext: ReturnType<typeof createConditionalVisitorContext>
  ipHash: string | null
  country: string | null
  deviceType: string
  referrerDomain: string | null
}> {
  const context = await createProfileRenderingContext(currentDomain)
  
  return {
    visitorContext: context.visitorContext,
    ipHash: context.visitorContext.ipHash,
    country: context.visitorContext.location.country,
    deviceType: context.visitorContext.device.type,
    referrerDomain: context.visitorContext.referrer.domain
  }
}

/**
 * Performance monitoring for conditional link evaluation
 */
export interface ConditionalLinkPerformanceMetrics {
  totalLinks: number
  evaluatedLinks: number
  visibleLinks: number
  totalEvaluationTime: number
  averageEvaluationTime: number
  conditionTypes: Record<string, number>
  matchedConditions: number
}

/**
 * Calculate performance metrics for conditional link evaluation
 */
export function calculatePerformanceMetrics(
  links: ConditionalLink[],
  evaluationResults: RuleEvaluationResult[]
): ConditionalLinkPerformanceMetrics {
  const totalEvaluationTime = evaluationResults.reduce(
    (sum, result) => sum + result.evaluationTime,
    0
  )
  
  const conditionTypes: Record<string, number> = {}
  let matchedConditions = 0
  
  links.forEach(link => {
    if (link.hasConditions) {
      link.conditions.forEach(condition => {
        conditionTypes[condition.type] = (conditionTypes[condition.type] || 0) + 1
      })
    }
  })
  
  evaluationResults.forEach(result => {
    if (result.matchedConditions.length > 0) {
      matchedConditions++
    }
  })
  
  return {
    totalLinks: links.length,
    evaluatedLinks: evaluationResults.length,
    visibleLinks: evaluationResults.filter(r => r.shouldShow).length,
    totalEvaluationTime,
    averageEvaluationTime: totalEvaluationTime / evaluationResults.length,
    conditionTypes,
    matchedConditions
  }
}

/**
 * Cache key generation for conditional link evaluation
 */
export function generateCacheKey(
  profileId: string,
  visitorContext: ReturnType<typeof createConditionalVisitorContext>
): string {
  // Create a cache key based on factors that affect link visibility
  const keyComponents = [
    profileId,
    visitorContext.device.type,
    visitorContext.device.platform,
    visitorContext.location.country || 'unknown',
    visitorContext.referrer.domain || 'direct',
    Math.floor(Date.now() / (5 * 60 * 1000)) // 5-minute time buckets for time-based rules
  ]
  
  return keyComponents.join(':')
}

/**
 * Validate conditional link configuration
 */
export function validateConditionalLink(link: ConditionalLink): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Basic validation
  if (!link.title.trim()) {
    errors.push('Link title is required')
  }
  
  if (!link.url.trim()) {
    errors.push('Link URL is required')
  }
  
  // URL validation
  try {
    new URL(link.url)
  } catch {
    errors.push('Invalid URL format')
  }
  
  // Schedule validation
  if (link.isScheduled) {
    if (!link.scheduleStart) {
      errors.push('Schedule start time is required when scheduling is enabled')
    }
    
    if (link.scheduleStart && link.scheduleEnd && link.scheduleStart >= link.scheduleEnd) {
      errors.push('Schedule start time must be before end time')
    }
  }
  
  // Condition validation
  if (link.hasConditions) {
    if (link.conditions.length === 0) {
      warnings.push('Link has conditions enabled but no conditions defined')
    }
    
    // Check for duplicate priorities
    const priorities = link.conditions.map(c => c.priority)
    const uniquePriorities = new Set(priorities)
    if (priorities.length !== uniquePriorities.size) {
      warnings.push('Multiple conditions have the same priority')
    }
    
    // Validate individual conditions
    link.conditions.forEach((condition, index) => {
      if (!condition.isActive) {
        return // Skip inactive conditions
      }
      
      switch (condition.type) {
        case 'referrer':
          const referrerRules = condition.rules as any
          if (!referrerRules.domains || referrerRules.domains.length === 0) {
            errors.push(`Referrer condition ${index + 1} has no domains specified`)
          }
          break
          
        case 'location':
          const locationRules = condition.rules as any
          if (!locationRules.countries && !locationRules.regions && !locationRules.cities) {
            errors.push(`Location condition ${index + 1} has no location criteria specified`)
          }
          break
          
        case 'device':
          const deviceRules = condition.rules as any
          if (!deviceRules.types && !deviceRules.platforms && !deviceRules.browsers) {
            errors.push(`Device condition ${index + 1} has no device criteria specified`)
          }
          break
          
        case 'time':
          const timeRules = condition.rules as any
          if (!timeRules.daysOfWeek || timeRules.daysOfWeek.length === 0) {
            errors.push(`Time condition ${index + 1} has no days of week specified`)
          }
          if (!timeRules.timeRanges || timeRules.timeRanges.length === 0) {
            errors.push(`Time condition ${index + 1} has no time ranges specified`)
          }
          break
      }
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
