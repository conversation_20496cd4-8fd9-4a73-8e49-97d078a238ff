import { UAParser } from 'ua-parser-js'
import { createHash } from 'crypto'

/**
 * Visitor context detection utilities specifically for conditional link evaluation
 * Builds on the existing visitor-context.ts but provides simplified interfaces
 * optimized for rule evaluation performance.
 */

/**
 * Device information for conditional link rules
 */
export interface ConditionalDeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop'
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown'
  browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'unknown'
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}

/**
 * Location information for conditional link rules
 */
export interface ConditionalLocationInfo {
  country: string | null
  region: string | null
  city: string | null
  timezone: string
}

/**
 * Referrer information for conditional link rules
 */
export interface ConditionalReferrerInfo {
  domain: string | null
  fullUrl: string | null
  isInternal: boolean
  isSocial: boolean
  isSearch: boolean
  source: string | null
}

/**
 * Complete visitor context for conditional link evaluation
 */
export interface ConditionalVisitorContext {
  device: ConditionalDeviceInfo
  location: ConditionalLocationInfo
  referrer: ConditionalReferrerInfo
  timestamp: Date
  ipHash: string | null
  userAgent: string
}

/**
 * Parse user agent for conditional link device detection
 */
export function parseDeviceForConditionalLinks(userAgent: string): ConditionalDeviceInfo {
  const parser = new UAParser(userAgent)
  const result = parser.getResult()

  // Determine device type
  let type: 'mobile' | 'tablet' | 'desktop' = 'desktop'
  if (result.device.type === 'mobile') {
    type = 'mobile'
  } else if (result.device.type === 'tablet') {
    type = 'tablet'
  }

  // Determine platform
  let platform: ConditionalDeviceInfo['platform'] = 'unknown'
  const osName = result.os.name?.toLowerCase() || ''
  if (osName.includes('ios') || osName.includes('iphone') || osName.includes('ipad')) {
    platform = 'ios'
  } else if (osName.includes('android')) {
    platform = 'android'
  } else if (osName.includes('windows')) {
    platform = 'windows'
  } else if (osName.includes('mac')) {
    platform = 'macos'
  } else if (osName.includes('linux')) {
    platform = 'linux'
  }

  // Determine browser
  let browser: ConditionalDeviceInfo['browser'] = 'unknown'
  const browserName = result.browser.name?.toLowerCase() || ''
  if (browserName.includes('chrome')) {
    browser = 'chrome'
  } else if (browserName.includes('firefox')) {
    browser = 'firefox'
  } else if (browserName.includes('safari')) {
    browser = 'safari'
  } else if (browserName.includes('edge')) {
    browser = 'edge'
  }

  return {
    type,
    platform,
    browser,
    isMobile: type === 'mobile',
    isTablet: type === 'tablet',
    isDesktop: type === 'desktop'
  }
}

/**
 * Extract location information from Vercel geo headers
 */
export function parseLocationForConditionalLinks(headers: Headers): ConditionalLocationInfo {
  // Try Vercel's geo headers first (available on Edge Runtime)
  const country = headers.get('x-vercel-ip-country')
  const region = headers.get('x-vercel-ip-country-region')
  const city = headers.get('x-vercel-ip-city')
  const timezone = headers.get('x-vercel-ip-timezone') || 'UTC'

  return {
    country: country || null,
    region: region || null,
    city: city || null,
    timezone
  }
}

/**
 * Parse referrer information for conditional link rules
 */
export function parseReferrerForConditionalLinks(
  referrer: string | null,
  currentDomain: string = 'localhost'
): ConditionalReferrerInfo {
  if (!referrer) {
    return {
      domain: null,
      fullUrl: null,
      isInternal: false,
      isSocial: false,
      isSearch: false,
      source: null
    }
  }

  try {
    const url = new URL(referrer)
    const domain = url.hostname.toLowerCase()
    const isInternal = domain === currentDomain.toLowerCase()

    // Check if it's a social media domain
    const socialDomains = [
      'facebook.com', 'instagram.com', 'twitter.com', 'x.com',
      'linkedin.com', 'tiktok.com', 'youtube.com', 'pinterest.com',
      'snapchat.com', 'reddit.com', 'tumblr.com'
    ]
    const isSocial = socialDomains.some(social => 
      domain === social || domain.endsWith(`.${social}`)
    )

    // Check if it's a search engine
    const searchDomains = [
      'google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com',
      'baidu.com', 'yandex.com', 'ask.com'
    ]
    const isSearch = searchDomains.some(search => 
      domain === search || domain.endsWith(`.${search}`)
    )

    // Extract source from UTM parameters or domain
    const source = url.searchParams.get('utm_source') || extractSourceFromDomain(domain)

    return {
      domain,
      fullUrl: referrer,
      isInternal,
      isSocial,
      isSearch,
      source
    }
  } catch (error) {
    // Invalid URL, return safe defaults
    return {
      domain: null,
      fullUrl: referrer,
      isInternal: false,
      isSocial: false,
      isSearch: false,
      source: null
    }
  }
}

/**
 * Extract source from domain for traffic analysis
 */
function extractSourceFromDomain(domain: string): string | null {
  // Social media sources
  if (domain.includes('facebook') || domain.includes('fb.')) return 'facebook'
  if (domain.includes('instagram')) return 'instagram'
  if (domain.includes('twitter') || domain.includes('x.com')) return 'twitter'
  if (domain.includes('linkedin')) return 'linkedin'
  if (domain.includes('tiktok')) return 'tiktok'
  if (domain.includes('youtube')) return 'youtube'
  if (domain.includes('pinterest')) return 'pinterest'
  
  // Search engines
  if (domain.includes('google')) return 'google'
  if (domain.includes('bing')) return 'bing'
  if (domain.includes('yahoo')) return 'yahoo'
  if (domain.includes('duckduckgo')) return 'duckduckgo'
  
  return null
}

/**
 * Detect timezone from location and browser headers
 */
export function detectTimezoneForConditionalLinks(
  locationTimezone?: string,
  acceptLanguage?: string
): string {
  // Use location timezone if available
  if (locationTimezone) {
    return locationTimezone
  }

  // Try to extract timezone from Accept-Language header
  if (acceptLanguage) {
    // This is a simplified approach - in production you might want more sophisticated parsing
    const locale = acceptLanguage.split(',')[0]
    if (locale.includes('US')) return 'America/New_York'
    if (locale.includes('GB')) return 'Europe/London'
    if (locale.includes('DE')) return 'Europe/Berlin'
    if (locale.includes('FR')) return 'Europe/Paris'
    if (locale.includes('JP')) return 'Asia/Tokyo'
    if (locale.includes('AU')) return 'Australia/Sydney'
  }

  // Default to UTC
  return 'UTC'
}

/**
 * Extract IP address from request headers
 */
export function extractIPAddressForConditionalLinks(headers: Headers): string | null {
  // Try various headers in order of preference
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-vercel-forwarded-for' // Vercel
  ]

  for (const header of ipHeaders) {
    const value = headers.get(header)
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim()
      if (isValidIP(ip)) {
        return ip
      }
    }
  }

  return null
}

/**
 * Simple IP validation
 */
function isValidIP(ip: string): boolean {
  // IPv4 regex
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  // IPv6 regex (simplified)
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip)
}

/**
 * Create privacy-safe IP hash for conditional link analytics
 */
export function hashIPForConditionalLinks(ip: string): string {
  if (!ip) return ''
  
  try {
    // Use a simple hash for conditional link evaluation
    // In production, you might want to use the more sophisticated hashing from privacy-utils.ts
    const hash = createHash('sha256')
    hash.update(ip + process.env.IP_HASH_SALT || 'default-salt')
    return hash.digest('hex').substring(0, 16) // Truncate for privacy
  } catch (error) {
    console.error('Error hashing IP for conditional links:', error)
    return ''
  }
}

/**
 * Create complete visitor context for conditional link evaluation
 */
export function createConditionalVisitorContext(
  userAgent: string,
  headers: Headers,
  referrer?: string,
  currentDomain: string = 'localhost'
): ConditionalVisitorContext {
  const ip = extractIPAddressForConditionalLinks(headers)
  const device = parseDeviceForConditionalLinks(userAgent)
  const location = parseLocationForConditionalLinks(headers)
  const referrerInfo = parseReferrerForConditionalLinks(referrer || null, currentDomain)
  const timezone = detectTimezoneForConditionalLinks(location.timezone, headers.get('accept-language') || undefined)

  return {
    device,
    location: {
      ...location,
      timezone
    },
    referrer: referrerInfo,
    timestamp: new Date(),
    ipHash: ip ? hashIPForConditionalLinks(ip) : null,
    userAgent
  }
}
