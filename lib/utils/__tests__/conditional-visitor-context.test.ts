import {
  parseDeviceForConditionalLinks,
  parseLocationForConditionalLinks,
  parseReferrerForConditionalLinks,
  detectTimezoneForConditionalLinks,
  extractIPAddressForConditionalLinks,
  hashIPForConditionalLinks,
  createConditionalVisitorContext,
  ConditionalDeviceInfo,
  ConditionalLocationInfo,
  ConditionalReferrerInfo,
  ConditionalVisitorContext
} from '../conditional-visitor-context'

describe('Conditional Visitor Context Detection', () => {
  describe('parseDeviceForConditionalLinks', () => {
    it('should parse Chrome desktop user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.type).toBe('desktop')
      expect(result.platform).toBe('windows')
      expect(result.browser).toBe('chrome')
      expect(result.isDesktop).toBe(true)
      expect(result.isMobile).toBe(false)
      expect(result.isTablet).toBe(false)
    })

    it('should parse iPhone Safari user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.type).toBe('mobile')
      expect(result.platform).toBe('ios')
      expect(result.browser).toBe('safari')
      expect(result.isMobile).toBe(true)
      expect(result.isDesktop).toBe(false)
      expect(result.isTablet).toBe(false)
    })

    it('should parse Android Chrome user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.type).toBe('mobile')
      expect(result.platform).toBe('android')
      expect(result.browser).toBe('chrome')
      expect(result.isMobile).toBe(true)
      expect(result.isDesktop).toBe(false)
      expect(result.isTablet).toBe(false)
    })

    it('should parse iPad user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.type).toBe('tablet')
      expect(result.platform).toBe('ios')
      expect(result.browser).toBe('safari')
      expect(result.isTablet).toBe(true)
      expect(result.isMobile).toBe(false)
      expect(result.isDesktop).toBe(false)
    })

    it('should handle Firefox user agent', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.browser).toBe('firefox')
      expect(result.platform).toBe('windows')
      expect(result.type).toBe('desktop')
    })

    it('should handle Edge user agent', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.browser).toBe('edge')
      expect(result.platform).toBe('windows')
      expect(result.type).toBe('desktop')
    })

    it('should handle unknown user agent gracefully', () => {
      const userAgent = 'Unknown/1.0'
      
      const result = parseDeviceForConditionalLinks(userAgent)
      
      expect(result.type).toBe('desktop') // Default fallback
      expect(result.platform).toBe('unknown')
      expect(result.browser).toBe('unknown')
    })

    it('should handle empty user agent', () => {
      const result = parseDeviceForConditionalLinks('')
      
      expect(result.type).toBe('desktop')
      expect(result.platform).toBe('unknown')
      expect(result.browser).toBe('unknown')
    })
  })

  describe('parseLocationForConditionalLinks', () => {
    it('should parse Vercel geo headers correctly', () => {
      const headers = new Headers({
        'x-vercel-ip-country': 'US',
        'x-vercel-ip-country-region': 'CA',
        'x-vercel-ip-city': 'San Francisco',
        'x-vercel-ip-timezone': 'America/Los_Angeles'
      })
      
      const result = parseLocationForConditionalLinks(headers)
      
      expect(result.country).toBe('US')
      expect(result.region).toBe('CA')
      expect(result.city).toBe('San Francisco')
      expect(result.timezone).toBe('America/Los_Angeles')
    })

    it('should handle missing geo headers', () => {
      const headers = new Headers()
      
      const result = parseLocationForConditionalLinks(headers)
      
      expect(result.country).toBeNull()
      expect(result.region).toBeNull()
      expect(result.city).toBeNull()
      expect(result.timezone).toBe('UTC')
    })

    it('should handle partial geo headers', () => {
      const headers = new Headers({
        'x-vercel-ip-country': 'GB',
        'x-vercel-ip-timezone': 'Europe/London'
      })
      
      const result = parseLocationForConditionalLinks(headers)
      
      expect(result.country).toBe('GB')
      expect(result.region).toBeNull()
      expect(result.city).toBeNull()
      expect(result.timezone).toBe('Europe/London')
    })
  })

  describe('parseReferrerForConditionalLinks', () => {
    it('should parse social media referrer correctly', () => {
      const referrer = 'https://www.instagram.com/user/profile'
      
      const result = parseReferrerForConditionalLinks(referrer, 'example.com')
      
      expect(result.domain).toBe('www.instagram.com')
      expect(result.fullUrl).toBe(referrer)
      expect(result.isInternal).toBe(false)
      expect(result.isSocial).toBe(true)
      expect(result.isSearch).toBe(false)
      expect(result.source).toBe('instagram')
    })

    it('should parse search engine referrer correctly', () => {
      const referrer = 'https://www.google.com/search?q=test'
      
      const result = parseReferrerForConditionalLinks(referrer, 'example.com')
      
      expect(result.domain).toBe('www.google.com')
      expect(result.isSearch).toBe(true)
      expect(result.isSocial).toBe(false)
      expect(result.source).toBe('google')
    })

    it('should detect internal referrer correctly', () => {
      const referrer = 'https://example.com/page1'
      
      const result = parseReferrerForConditionalLinks(referrer, 'example.com')
      
      expect(result.domain).toBe('example.com')
      expect(result.isInternal).toBe(true)
      expect(result.isSocial).toBe(false)
      expect(result.isSearch).toBe(false)
    })

    it('should handle UTM parameters', () => {
      const referrer = 'https://example.com/page?utm_source=newsletter&utm_medium=email'
      
      const result = parseReferrerForConditionalLinks(referrer, 'mysite.com')
      
      expect(result.source).toBe('newsletter')
    })

    it('should handle null referrer', () => {
      const result = parseReferrerForConditionalLinks(null, 'example.com')
      
      expect(result.domain).toBeNull()
      expect(result.fullUrl).toBeNull()
      expect(result.isInternal).toBe(false)
      expect(result.isSocial).toBe(false)
      expect(result.isSearch).toBe(false)
      expect(result.source).toBeNull()
    })

    it('should handle invalid URL gracefully', () => {
      const result = parseReferrerForConditionalLinks('not-a-url', 'example.com')
      
      expect(result.domain).toBeNull()
      expect(result.fullUrl).toBe('not-a-url')
      expect(result.isInternal).toBe(false)
    })

    it('should recognize various social media domains', () => {
      const socialDomains = [
        'facebook.com',
        'twitter.com',
        'x.com',
        'linkedin.com',
        'tiktok.com',
        'youtube.com',
        'pinterest.com'
      ]

      socialDomains.forEach(domain => {
        const result = parseReferrerForConditionalLinks(`https://${domain}/page`, 'example.com')
        expect(result.isSocial).toBe(true)
      })
    })
  })

  describe('detectTimezoneForConditionalLinks', () => {
    it('should use location timezone when available', () => {
      const result = detectTimezoneForConditionalLinks('America/New_York', 'en-US')
      expect(result).toBe('America/New_York')
    })

    it('should fallback to Accept-Language parsing', () => {
      const result = detectTimezoneForConditionalLinks(undefined, 'en-US')
      expect(result).toBe('America/New_York')
    })

    it('should handle various locales', () => {
      expect(detectTimezoneForConditionalLinks(undefined, 'en-GB')).toBe('Europe/London')
      expect(detectTimezoneForConditionalLinks(undefined, 'de-DE')).toBe('Europe/Berlin')
      expect(detectTimezoneForConditionalLinks(undefined, 'ja-JP')).toBe('Asia/Tokyo')
    })

    it('should default to UTC when no information available', () => {
      const result = detectTimezoneForConditionalLinks(undefined, undefined)
      expect(result).toBe('UTC')
    })

    it('should default to UTC for unknown locales', () => {
      const result = detectTimezoneForConditionalLinks(undefined, 'xx-XX')
      expect(result).toBe('UTC')
    })
  })

  describe('extractIPAddressForConditionalLinks', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********, ********'
      })
      
      const result = extractIPAddressForConditionalLinks(headers)
      expect(result).toBe('***********')
    })

    it('should try multiple headers in order', () => {
      const headers = new Headers({
        'x-real-ip': '***********'
      })
      
      const result = extractIPAddressForConditionalLinks(headers)
      expect(result).toBe('***********')
    })

    it('should handle Vercel headers', () => {
      const headers = new Headers({
        'x-vercel-forwarded-for': '***********'
      })
      
      const result = extractIPAddressForConditionalLinks(headers)
      expect(result).toBe('***********')
    })

    it('should return null when no valid IP found', () => {
      const headers = new Headers()
      
      const result = extractIPAddressForConditionalLinks(headers)
      expect(result).toBeNull()
    })

    it('should validate IP format', () => {
      const headers = new Headers({
        'x-forwarded-for': 'invalid-ip'
      })
      
      const result = extractIPAddressForConditionalLinks(headers)
      expect(result).toBeNull()
    })
  })

  describe('hashIPForConditionalLinks', () => {
    it('should create consistent hash for same IP', () => {
      const ip = '***********'
      const hash1 = hashIPForConditionalLinks(ip)
      const hash2 = hashIPForConditionalLinks(ip)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toHaveLength(16)
    })

    it('should create different hashes for different IPs', () => {
      const hash1 = hashIPForConditionalLinks('***********')
      const hash2 = hashIPForConditionalLinks('***********')
      
      expect(hash1).not.toBe(hash2)
    })

    it('should handle empty IP', () => {
      const result = hashIPForConditionalLinks('')
      expect(result).toBe('')
    })
  })

  describe('createConditionalVisitorContext', () => {
    it('should create complete visitor context', () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
      const headers = new Headers({
        'x-vercel-ip-country': 'US',
        'x-vercel-ip-city': 'New York',
        'x-vercel-ip-timezone': 'America/New_York',
        'x-forwarded-for': '***********',
        'accept-language': 'en-US,en;q=0.9'
      })
      const referrer = 'https://www.instagram.com/user'
      
      const result = createConditionalVisitorContext(userAgent, headers, referrer, 'example.com')
      
      expect(result.device.type).toBe('mobile')
      expect(result.device.platform).toBe('ios')
      expect(result.device.browser).toBe('safari')
      expect(result.location.country).toBe('US')
      expect(result.location.city).toBe('New York')
      expect(result.location.timezone).toBe('America/New_York')
      expect(result.referrer.domain).toBe('www.instagram.com')
      expect(result.referrer.isSocial).toBe(true)
      expect(result.ipHash).toBeTruthy()
      expect(result.userAgent).toBe(userAgent)
      expect(result.timestamp).toBeInstanceOf(Date)
    })

    it('should handle minimal headers', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      const headers = new Headers()
      
      const result = createConditionalVisitorContext(userAgent, headers)
      
      expect(result.device.type).toBe('desktop')
      expect(result.location.country).toBeNull()
      expect(result.location.timezone).toBe('UTC')
      expect(result.referrer.domain).toBeNull()
      expect(result.ipHash).toBeNull()
    })
  })
})
