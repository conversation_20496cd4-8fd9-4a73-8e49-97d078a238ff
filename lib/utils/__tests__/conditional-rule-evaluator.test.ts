import {
  ConditionalRuleEvaluator,
  evaluateConditionalLinks,
  ConditionalLink,
  LinkCondition,
  ReferrerRule,
  LocationRule,
  DeviceRule,
  TimeRule,
  ConditionAction
} from '../conditional-rule-evaluator'
import { ConditionalVisitorContext } from '../conditional-visitor-context'

// Mock helper functions
function createMockVisitorContext(overrides: Partial<ConditionalVisitorContext> = {}): ConditionalVisitorContext {
  return {
    device: {
      type: 'desktop',
      platform: 'windows',
      browser: 'chrome',
      isMobile: false,
      isTablet: false,
      isDesktop: true
    },
    location: {
      country: 'US',
      region: 'CA',
      city: 'San Francisco',
      timezone: 'America/Los_Angeles'
    },
    referrer: {
      domain: 'google.com',
      fullUrl: 'https://google.com/search?q=test',
      isInternal: false,
      isSocial: false,
      isSearch: true,
      source: 'google'
    },
    timestamp: new Date('2025-01-15T10:00:00Z'),
    ipHash: 'abc123def456',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ...overrides
  }
}

function createMockLink(overrides: Partial<ConditionalLink> = {}): ConditionalLink {
  return {
    id: 'link-1',
    title: 'Test Link',
    url: 'https://example.com',
    icon: 'icon.png',
    isVisible: true,
    order: 0,
    isScheduled: false,
    hasConditions: false,
    conditions: [],
    defaultBehavior: 'show',
    ...overrides
  }
}

function createMockCondition(
  type: 'referrer' | 'location' | 'device' | 'time',
  rules: any,
  action: ConditionAction = { type: 'show' },
  overrides: Partial<LinkCondition> = {}
): LinkCondition {
  return {
    id: `condition-${type}`,
    linkId: 'link-1',
    type,
    priority: 0,
    isActive: true,
    rules,
    action,
    ...overrides
  }
}

describe('ConditionalRuleEvaluator', () => {
  describe('Schedule Evaluation', () => {
    it('should hide link before start time', async () => {
      const link = createMockLink({
        isScheduled: true,
        scheduleStart: new Date('2025-01-15T12:00:00Z'),
        scheduleEnd: new Date('2025-01-15T18:00:00Z')
      })
      
      const context = createMockVisitorContext()
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-15T10:00:00Z'))
      
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(false)
      expect(result.matchedConditions).toContain('schedule')
    })

    it('should show link during active schedule', async () => {
      const link = createMockLink({
        isScheduled: true,
        scheduleStart: new Date('2025-01-15T09:00:00Z'),
        scheduleEnd: new Date('2025-01-15T18:00:00Z')
      })
      
      const context = createMockVisitorContext()
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-15T12:00:00Z'))
      
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
    })

    it('should hide link after end time', async () => {
      const link = createMockLink({
        isScheduled: true,
        scheduleStart: new Date('2025-01-15T09:00:00Z'),
        scheduleEnd: new Date('2025-01-15T12:00:00Z')
      })
      
      const context = createMockVisitorContext()
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-15T15:00:00Z'))
      
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(false)
      expect(result.matchedConditions).toContain('schedule')
    })
  })

  describe('Referrer Conditions', () => {
    it('should match exact referrer domain', async () => {
      const rules: ReferrerRule = {
        domains: ['google.com'],
        matchType: 'exact',
        caseSensitive: false
      }
      
      const condition = createMockCondition('referrer', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        referrer: {
          domain: 'google.com',
          fullUrl: 'https://google.com/search',
          isInternal: false,
          isSocial: false,
          isSearch: true,
          source: 'google'
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
      expect(result.matchedConditions).toContain('condition-referrer')
    })

    it('should match referrer with contains', async () => {
      const rules: ReferrerRule = {
        domains: ['instagram'],
        matchType: 'contains',
        caseSensitive: false
      }
      
      const condition = createMockCondition('referrer', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        referrer: {
          domain: 'www.instagram.com',
          fullUrl: 'https://www.instagram.com/user',
          isInternal: false,
          isSocial: true,
          isSearch: false,
          source: 'instagram'
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
    })

    it('should not match when referrer is null', async () => {
      const rules: ReferrerRule = {
        domains: ['google.com'],
        matchType: 'exact',
        caseSensitive: false
      }
      
      const condition = createMockCondition('referrer', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        defaultBehavior: 'hide'
      })
      
      const context = createMockVisitorContext({
        referrer: {
          domain: null,
          fullUrl: null,
          isInternal: false,
          isSocial: false,
          isSearch: false,
          source: null
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(false)
    })
  })

  describe('Location Conditions', () => {
    it('should match country rule', async () => {
      const rules: LocationRule = {
        countries: ['US', 'CA']
      }
      
      const condition = createMockCondition('location', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        location: {
          country: 'US',
          region: 'CA',
          city: 'San Francisco',
          timezone: 'America/Los_Angeles'
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
      expect(result.matchedConditions).toContain('condition-location')
    })

    it('should exclude countries correctly', async () => {
      const rules: LocationRule = {
        countries: ['US', 'CA'],
        excludeCountries: ['US']
      }
      
      const condition = createMockCondition('location', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        defaultBehavior: 'hide'
      })
      
      const context = createMockVisitorContext({
        location: {
          country: 'US',
          region: 'CA',
          city: 'San Francisco',
          timezone: 'America/Los_Angeles'
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(false)
    })

    it('should match region rule', async () => {
      const rules: LocationRule = {
        regions: ['CA', 'NY']
      }
      
      const condition = createMockCondition('location', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        location: {
          country: 'US',
          region: 'CA',
          city: 'San Francisco',
          timezone: 'America/Los_Angeles'
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
    })
  })

  describe('Device Conditions', () => {
    it('should match device type', async () => {
      const rules: DeviceRule = {
        types: ['mobile', 'tablet']
      }
      
      const condition = createMockCondition('device', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        device: {
          type: 'mobile',
          platform: 'ios',
          browser: 'safari',
          isMobile: true,
          isTablet: false,
          isDesktop: false
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
      expect(result.matchedConditions).toContain('condition-device')
    })

    it('should match platform', async () => {
      const rules: DeviceRule = {
        platforms: ['ios', 'android']
      }
      
      const condition = createMockCondition('device', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        device: {
          type: 'mobile',
          platform: 'android',
          browser: 'chrome',
          isMobile: true,
          isTablet: false,
          isDesktop: false
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
    })

    it('should match browser', async () => {
      const rules: DeviceRule = {
        browsers: ['chrome', 'firefox']
      }
      
      const condition = createMockCondition('device', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })
      
      const context = createMockVisitorContext({
        device: {
          type: 'desktop',
          platform: 'windows',
          browser: 'chrome',
          isMobile: false,
          isTablet: false,
          isDesktop: true
        }
      })
      
      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)
      
      expect(result.shouldShow).toBe(true)
    })
  })

  describe('Time Conditions', () => {
    it('should match day of week and time range', async () => {
      const rules: TimeRule = {
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        timeRanges: [
          { start: '09:00', end: '17:00' }
        ],
        timezone: 'America/Los_Angeles'
      }

      const condition = createMockCondition('time', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition]
      })

      const context = createMockVisitorContext()
      // Tuesday at 2 PM PST
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-14T22:00:00Z'))

      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(true)
      expect(result.matchedConditions).toContain('condition-time')
    })

    it('should not match outside time range', async () => {
      const rules: TimeRule = {
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        timeRanges: [
          { start: '09:00', end: '17:00' }
        ],
        timezone: 'America/Los_Angeles'
      }

      const condition = createMockCondition('time', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        defaultBehavior: 'hide'
      })

      const context = createMockVisitorContext()
      // Tuesday at 8 PM PST (outside business hours)
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-15T04:00:00Z'))

      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(false)
    })

    it('should not match on weekend', async () => {
      const rules: TimeRule = {
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        timeRanges: [
          { start: '09:00', end: '17:00' }
        ],
        timezone: 'America/Los_Angeles'
      }

      const condition = createMockCondition('time', rules)
      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        defaultBehavior: 'hide'
      })

      const context = createMockVisitorContext()
      // Saturday at 2 PM PST
      const evaluator = new ConditionalRuleEvaluator(context, new Date('2025-01-18T22:00:00Z'))

      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(false)
    })
  })

  describe('Priority and Fallback', () => {
    it('should apply higher priority condition first', async () => {
      const lowPriorityCondition = createMockCondition('referrer', {
        domains: ['google.com'],
        matchType: 'exact',
        caseSensitive: false
      }, { type: 'show' }, { priority: 1 })

      const highPriorityCondition = createMockCondition('device', {
        types: ['desktop']
      }, { type: 'hide' }, { priority: 10 })

      const link = createMockLink({
        hasConditions: true,
        conditions: [lowPriorityCondition, highPriorityCondition]
      })

      const context = createMockVisitorContext({
        referrer: {
          domain: 'google.com',
          fullUrl: 'https://google.com/search',
          isInternal: false,
          isSocial: false,
          isSearch: true,
          source: 'google'
        },
        device: {
          type: 'desktop',
          platform: 'windows',
          browser: 'chrome',
          isMobile: false,
          isTablet: false,
          isDesktop: true
        }
      })

      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)

      // Should apply high priority condition (hide) even though low priority would show
      expect(result.shouldShow).toBe(false)
      expect(result.matchedConditions).toContain('condition-device')
    })

    it('should use default behavior when no conditions match', async () => {
      const condition = createMockCondition('referrer', {
        domains: ['facebook.com'],
        matchType: 'exact',
        caseSensitive: false
      })

      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        defaultBehavior: 'show'
      })

      const context = createMockVisitorContext({
        referrer: {
          domain: 'google.com', // Doesn't match condition
          fullUrl: 'https://google.com/search',
          isInternal: false,
          isSocial: false,
          isSearch: true,
          source: 'google'
        }
      })

      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(true)
      expect(result.matchedConditions).toHaveLength(0)
    })

    it('should respect isVisible flag in default behavior', async () => {
      const link = createMockLink({
        hasConditions: false,
        isVisible: false,
        defaultBehavior: 'show'
      })

      const context = createMockVisitorContext()
      const evaluator = new ConditionalRuleEvaluator(context)

      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(false)
    })
  })

  describe('Alternate Content', () => {
    it('should apply alternate title and icon', async () => {
      const condition = createMockCondition('device', {
        types: ['mobile']
      }, {
        type: 'show',
        alternateTitle: 'Mobile App',
        alternateIcon: 'mobile-icon.png'
      })

      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        title: 'Desktop App',
        icon: 'desktop-icon.png'
      })

      const context = createMockVisitorContext({
        device: {
          type: 'mobile',
          platform: 'ios',
          browser: 'safari',
          isMobile: true,
          isTablet: false,
          isDesktop: false
        }
      })

      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(true)
      expect(result.effectiveTitle).toBe('Mobile App')
      expect(result.effectiveIcon).toBe('mobile-icon.png')
    })

    it('should handle redirect action', async () => {
      const condition = createMockCondition('location', {
        countries: ['CA']
      }, {
        type: 'redirect',
        value: 'https://canada.example.com'
      })

      const link = createMockLink({
        hasConditions: true,
        conditions: [condition],
        url: 'https://example.com'
      })

      const context = createMockVisitorContext({
        location: {
          country: 'CA',
          region: 'ON',
          city: 'Toronto',
          timezone: 'America/Toronto'
        }
      })

      const evaluator = new ConditionalRuleEvaluator(context)
      const result = await evaluator.evaluateLink(link)

      expect(result.shouldShow).toBe(false) // Redirect means don't show original
      expect(result.effectiveUrl).toBe('https://canada.example.com')
    })
  })

  describe('evaluateConditionalLinks', () => {
    it('should evaluate multiple links', async () => {
      const link1 = createMockLink({
        id: 'link-1',
        title: 'Link 1',
        hasConditions: true,
        conditions: [createMockCondition('device', { types: ['mobile'] })],
        defaultBehavior: 'hide'
      })

      const link2 = createMockLink({
        id: 'link-2',
        title: 'Link 2',
        hasConditions: false
      })

      const context = createMockVisitorContext({
        device: {
          type: 'desktop',
          platform: 'windows',
          browser: 'chrome',
          isMobile: false,
          isTablet: false,
          isDesktop: true
        }
      })

      const results = await evaluateConditionalLinks([link1, link2], context)

      expect(results).toHaveLength(2)
      expect(results[0].linkId).toBe('link-1')
      expect(results[0].shouldShow).toBe(false) // Desktop doesn't match mobile condition
      expect(results[1].linkId).toBe('link-2')
      expect(results[1].shouldShow).toBe(true) // No conditions, default show
    })
  })
})
