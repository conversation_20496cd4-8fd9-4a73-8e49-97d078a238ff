/**
 * Integration example for conditional visitor context detection system
 * 
 * This file demonstrates how to integrate the conditional visitor context
 * system into your Next.js application for dynamic link rendering.
 */

import { headers } from 'next/headers'
import { createConditionalVisitorContext } from './conditional-visitor-context'
import { ConditionalRuleEvaluator, ConditionalLink } from './conditional-rule-evaluator'
import { renderConditionalLinks, createProfileRenderingContext } from './conditional-link-renderer'

/**
 * Example: Server Component with conditional link rendering
 */
export async function ProfilePageExample({ username }: { username: string }) {
  // Get links from database (this would be your actual data fetching)
  const links = await getLinksForProfile(username)
  
  // Render links with conditional evaluation
  const renderedLinks = await renderConditionalLinks(links)
  
  return (
    <div className="profile-links">
      {renderedLinks.map(link => (
        <LinkComponent
          key={link.id}
          title={link.effectiveTitle}
          url={link.effectiveUrl}
          icon={link.effectiveIcon}
          onClick={() => trackLinkClick(link.id, link.matchedConditions)}
        />
      ))}
    </div>
  )
}

/**
 * Example: API Route with visitor context tracking
 */
export async function trackProfileViewExample(profileId: string) {
  const headersList = headers()
  const userAgent = headersList.get('user-agent') || ''
  const referrer = headersList.get('referer')
  const host = headersList.get('host')
  
  // Create visitor context for analytics
  const visitorContext = createConditionalVisitorContext(
    userAgent,
    headersList,
    referrer,
    host?.split(':')[0] || 'localhost'
  )
  
  // Track profile view with enhanced context
  await trackProfileView({
    profileId,
    visitorContext: {
      deviceType: visitorContext.device.type,
      platform: visitorContext.device.platform,
      browser: visitorContext.device.browser,
      country: visitorContext.location.country,
      referrerDomain: visitorContext.referrer.domain,
      referrerSource: visitorContext.referrer.source,
      isInternalReferrer: visitorContext.referrer.isInternal,
      isSocialReferrer: visitorContext.referrer.isSocial,
      ipHash: visitorContext.ipHash,
      timestamp: visitorContext.timestamp
    }
  })
}

/**
 * Example: Link click tracking with conditional context
 */
export async function trackLinkClickExample(
  linkId: string,
  matchedConditions: string[]
) {
  const context = await createProfileRenderingContext()
  
  await trackLinkClick({
    linkId,
    visitorContext: context.visitorContext,
    matchedConditions,
    conditionTypes: matchedConditions.map(id => getConditionType(id)),
    timestamp: new Date()
  })
}

/**
 * Example: Real-time link evaluation for dynamic content
 */
export async function evaluateLinkVisibilityExample(
  linkId: string
): Promise<{
  shouldShow: boolean
  effectiveTitle: string
  effectiveUrl: string
  matchedConditions: string[]
}> {
  // Get link with conditions from database
  const link = await getLinkWithConditions(linkId)
  
  if (!link) {
    throw new Error('Link not found')
  }
  
  // Create visitor context
  const headersList = headers()
  const userAgent = headersList.get('user-agent') || ''
  const referrer = headersList.get('referer')
  const host = headersList.get('host')
  
  const visitorContext = createConditionalVisitorContext(
    userAgent,
    headersList,
    referrer,
    host?.split(':')[0] || 'localhost'
  )
  
  // Evaluate link
  const evaluator = new ConditionalRuleEvaluator(visitorContext)
  const result = await evaluator.evaluateLink(link)
  
  return {
    shouldShow: result.shouldShow,
    effectiveTitle: result.effectiveTitle || link.title,
    effectiveUrl: result.effectiveUrl || link.url,
    matchedConditions: result.matchedConditions
  }
}

/**
 * Example: Batch evaluation for profile optimization
 */
export async function optimizeProfileLinksExample(
  profileId: string
): Promise<{
  visibleLinks: number
  hiddenLinks: number
  conditionMatches: Record<string, number>
  performanceMetrics: {
    totalEvaluationTime: number
    averageEvaluationTime: number
  }
}> {
  const links = await getLinksForProfile(profileId)
  const context = await createProfileRenderingContext()
  
  // Evaluate all links
  const evaluator = new ConditionalRuleEvaluator(context.visitorContext)
  const results = await Promise.all(
    links.map(link => evaluator.evaluateLink(link))
  )
  
  // Calculate metrics
  const visibleLinks = results.filter(r => r.shouldShow).length
  const hiddenLinks = results.length - visibleLinks
  const totalEvaluationTime = results.reduce((sum, r) => sum + r.evaluationTime, 0)
  
  // Count condition matches
  const conditionMatches: Record<string, number> = {}
  results.forEach(result => {
    result.matchedConditions.forEach(conditionId => {
      conditionMatches[conditionId] = (conditionMatches[conditionId] || 0) + 1
    })
  })
  
  return {
    visibleLinks,
    hiddenLinks,
    conditionMatches,
    performanceMetrics: {
      totalEvaluationTime,
      averageEvaluationTime: totalEvaluationTime / results.length
    }
  }
}

/**
 * Example: A/B testing with conditional links
 */
export async function abTestConditionalLinksExample(
  profileId: string,
  testVariant: 'A' | 'B'
): Promise<ConditionalLink[]> {
  const baseLinks = await getLinksForProfile(profileId)
  
  // Modify links based on test variant
  const testLinks = baseLinks.map(link => {
    if (testVariant === 'B' && link.hasConditions) {
      // Variant B: More aggressive mobile targeting
      const mobileConditions = link.conditions.filter(c => 
        c.type === 'device' && 
        (c.rules as any).types?.includes('mobile')
      )
      
      if (mobileConditions.length > 0) {
        return {
          ...link,
          conditions: mobileConditions.map(c => ({
            ...c,
            priority: c.priority + 10 // Higher priority for mobile
          }))
        }
      }
    }
    
    return link
  })
  
  return testLinks
}

/**
 * Example: Conditional link analytics dashboard data
 */
export async function getConditionalLinkAnalyticsExample(
  profileId: string,
  dateRange: { start: Date; end: Date }
): Promise<{
  totalViews: number
  conditionalViews: number
  conditionBreakdown: Record<string, {
    views: number
    clicks: number
    conversionRate: number
  }>
  deviceBreakdown: Record<string, number>
  locationBreakdown: Record<string, number>
  referrerBreakdown: Record<string, number>
}> {
  // This would query your analytics database
  const analytics = await getAnalyticsData(profileId, dateRange)
  
  // Process conditional link specific metrics
  const conditionBreakdown: Record<string, any> = {}
  const deviceBreakdown: Record<string, number> = {}
  const locationBreakdown: Record<string, number> = {}
  const referrerBreakdown: Record<string, number> = {}
  
  analytics.forEach(record => {
    // Process condition matches
    if (record.matchedConditions) {
      record.matchedConditions.forEach((conditionId: string) => {
        if (!conditionBreakdown[conditionId]) {
          conditionBreakdown[conditionId] = { views: 0, clicks: 0, conversionRate: 0 }
        }
        conditionBreakdown[conditionId].views++
        if (record.clicked) {
          conditionBreakdown[conditionId].clicks++
        }
      })
    }
    
    // Process device breakdown
    if (record.deviceType) {
      deviceBreakdown[record.deviceType] = (deviceBreakdown[record.deviceType] || 0) + 1
    }
    
    // Process location breakdown
    if (record.country) {
      locationBreakdown[record.country] = (locationBreakdown[record.country] || 0) + 1
    }
    
    // Process referrer breakdown
    if (record.referrerDomain) {
      referrerBreakdown[record.referrerDomain] = (referrerBreakdown[record.referrerDomain] || 0) + 1
    }
  })
  
  // Calculate conversion rates
  Object.keys(conditionBreakdown).forEach(conditionId => {
    const condition = conditionBreakdown[conditionId]
    condition.conversionRate = condition.views > 0 ? 
      (condition.clicks / condition.views) * 100 : 0
  })
  
  return {
    totalViews: analytics.length,
    conditionalViews: analytics.filter((r: any) => r.matchedConditions?.length > 0).length,
    conditionBreakdown,
    deviceBreakdown,
    locationBreakdown,
    referrerBreakdown
  }
}

// Mock functions (replace with your actual implementations)
async function getLinksForProfile(profileId: string): Promise<ConditionalLink[]> {
  // Your database query here
  return []
}

async function getLinkWithConditions(linkId: string): Promise<ConditionalLink | null> {
  // Your database query here
  return null
}

async function trackProfileView(data: any): Promise<void> {
  // Your analytics tracking here
}

async function trackLinkClick(data: any): Promise<void> {
  // Your analytics tracking here
}

async function getAnalyticsData(profileId: string, dateRange: any): Promise<any[]> {
  // Your analytics query here
  return []
}

function getConditionType(conditionId: string): string {
  // Your condition lookup here
  return 'unknown'
}

function LinkComponent({ title, url, icon, onClick }: any) {
  // Your link component here
  return null
}
