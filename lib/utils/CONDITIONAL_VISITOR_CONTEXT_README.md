# Conditional Visitor Context Detection System

A specialized visitor context detection system designed for conditional link evaluation in the LinksInBio application. This system builds on the existing visitor context infrastructure but provides optimized interfaces specifically for rule-based link visibility and content adaptation.

## Features

### 🎯 Conditional Link Optimization
- **Performance-Optimized**: Streamlined data structures for fast rule evaluation
- **Rule-Specific Context**: Tailored visitor information for different condition types
- **Server-Side Evaluation**: Optimized for Next.js App Router server components
- **Caching-Ready**: Designed for efficient caching of evaluation results

### 🔍 Device Detection for Conditional Links
- **Simplified Device Types**: Mobile, tablet, desktop categorization
- **Platform Detection**: iOS, Android, Windows, macOS, Linux identification
- **Browser Detection**: Chrome, Firefox, Safari, Edge recognition
- **Rule-Friendly Format**: Optimized for device-based conditional rules

### 🌍 Location Detection for Geographic Rules
- **Vercel Geo Integration**: Uses Vercel's edge geo headers when available
- **Country/Region/City**: Hierarchical location matching
- **Timezone Awareness**: Accurate timezone detection for time-based rules
- **Privacy-Compliant**: No precise coordinates, only necessary location data

### 🔗 Referrer Analysis for Traffic-Based Rules
- **Domain Extraction**: Clean domain parsing for referrer matching
- **Traffic Source Classification**: Social, search, internal, external categorization
- **UTM Parameter Support**: Extracts campaign tracking information
- **Pattern Matching**: Supports exact, contains, and regex matching

## Installation

The system uses the existing `ua-parser-js` dependency:

```bash
bun add ua-parser-js
bun add --dev @types/ua-parser-js
```

## Usage

### Basic Visitor Context Creation

```typescript
import { createConditionalVisitorContext } from '@/lib/utils/conditional-visitor-context'
import { headers } from 'next/headers'

// In a Server Component or API route
export default async function ProfilePage() {
  const headersList = headers()
  const userAgent = headersList.get('user-agent') || ''
  const referrer = headersList.get('referer')
  
  const visitorContext = createConditionalVisitorContext(
    userAgent,
    headersList,
    referrer,
    'mysite.com'
  )
  
  // Use context for conditional link evaluation
  console.log('Device:', visitorContext.device.type)
  console.log('Location:', visitorContext.location.country)
  console.log('Referrer:', visitorContext.referrer.domain)
}
```

### Rule Evaluation

```typescript
import { ConditionalRuleEvaluator } from '@/lib/utils/conditional-rule-evaluator'

// Create evaluator with visitor context
const evaluator = new ConditionalRuleEvaluator(visitorContext)

// Evaluate a link with conditions
const result = await evaluator.evaluateLink(linkWithConditions)

if (result.shouldShow) {
  console.log('Show link:', result.effectiveTitle)
  console.log('URL:', result.effectiveUrl)
  console.log('Matched conditions:', result.matchedConditions)
}
```

### Server-Side Rendering Integration

```typescript
import { renderConditionalLinks } from '@/lib/utils/conditional-link-renderer'

// In a Server Component
export default async function ProfilePage({ params }: { params: { username: string } }) {
  const links = await getLinksForProfile(params.username)
  
  // Automatically detects visitor context and evaluates conditions
  const renderedLinks = await renderConditionalLinks(links)
  
  return (
    <div>
      {renderedLinks.map(link => (
        <LinkComponent
          key={link.id}
          title={link.effectiveTitle}
          url={link.effectiveUrl}
          icon={link.effectiveIcon}
        />
      ))}
    </div>
  )
}
```

## Data Structures

### ConditionalVisitorContext

```typescript
interface ConditionalVisitorContext {
  device: {
    type: 'mobile' | 'tablet' | 'desktop'
    platform: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown'
    browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'unknown'
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
  }
  location: {
    country: string | null
    region: string | null
    city: string | null
    timezone: string
  }
  referrer: {
    domain: string | null
    fullUrl: string | null
    isInternal: boolean
    isSocial: boolean
    isSearch: boolean
    source: string | null
  }
  timestamp: Date
  ipHash: string | null
  userAgent: string
}
```

### Rule Configuration Examples

#### Device-Based Rule
```typescript
const deviceRule: DeviceRule = {
  types: ['mobile', 'tablet'],
  platforms: ['ios', 'android'],
  browsers: ['safari', 'chrome']
}
```

#### Location-Based Rule
```typescript
const locationRule: LocationRule = {
  countries: ['US', 'CA', 'GB'],
  excludeCountries: ['CN'],
  regions: ['CA', 'NY', 'TX']
}
```

#### Referrer-Based Rule
```typescript
const referrerRule: ReferrerRule = {
  domains: ['instagram.com', 'facebook.com'],
  matchType: 'contains',
  caseSensitive: false
}
```

#### Time-Based Rule
```typescript
const timeRule: TimeRule = {
  daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
  timeRanges: [
    { start: '09:00', end: '12:00' },
    { start: '13:00', end: '17:00' }
  ],
  timezone: 'America/New_York'
}
```

## Performance Considerations

### Caching Strategy

```typescript
import { generateCacheKey } from '@/lib/utils/conditional-link-renderer'

// Generate cache key for evaluation results
const cacheKey = generateCacheKey(profileId, visitorContext)

// Cache evaluation results for 5 minutes
const cachedResult = await cache.get(cacheKey)
if (cachedResult) {
  return cachedResult
}

const result = await evaluateConditionalLinks(links, visitorContext)
await cache.set(cacheKey, result, 300) // 5 minutes TTL
```

### Performance Monitoring

```typescript
import { calculatePerformanceMetrics } from '@/lib/utils/conditional-link-renderer'

const metrics = calculatePerformanceMetrics(links, evaluationResults)

console.log('Evaluation metrics:', {
  totalTime: metrics.totalEvaluationTime,
  averageTime: metrics.averageEvaluationTime,
  visibleLinks: metrics.visibleLinks,
  conditionTypes: metrics.conditionTypes
})
```

## Testing

### Unit Tests

```bash
# Run conditional visitor context tests
bun test lib/utils/__tests__/conditional-visitor-context.test.ts

# Run rule evaluator tests
bun test lib/utils/__tests__/conditional-rule-evaluator.test.ts

# Run all conditional link tests
bun test lib/utils/__tests__/conditional-*
```

### Test Coverage

- ✅ Device parsing (8 test cases)
- ✅ Location detection (4 test cases)
- ✅ Referrer analysis (8 test cases)
- ✅ Timezone detection (5 test cases)
- ✅ IP handling (5 test cases)
- ✅ Context creation (2 test cases)
- ✅ Rule evaluation (15+ test cases)
- ✅ Priority handling (3 test cases)
- ✅ Performance metrics (1 test case)

## Privacy & Security

### IP Address Handling
- **Hashing**: All IP addresses are hashed before storage
- **Truncation**: Hashes are truncated to 16 characters for privacy
- **Salt Rotation**: Uses environment-based salt for hashing

### Data Minimization
- **Location**: Only country/region/city, no precise coordinates
- **Device**: Only necessary information for rule evaluation
- **Referrer**: Domain-level information only, no full URLs in storage

### GDPR Compliance
- **Consent-Aware**: Can filter context based on user consent
- **Data Retention**: Designed for automatic data cleanup
- **Anonymization**: Built-in data anonymization for long-term storage

## Environment Variables

```env
# IP hashing salt (should be rotated periodically)
IP_HASH_SALT=your-secret-salt-here

# Optional: Disable specific detection features
CONDITIONAL_LINKS_ENABLE_GEOLOCATION=true
CONDITIONAL_LINKS_ENABLE_DEVICE_DETECTION=true
CONDITIONAL_LINKS_ENABLE_REFERRER_TRACKING=true
```

## Integration with Existing Systems

This conditional visitor context system is designed to work alongside the existing visitor context system:

- **Complementary**: Doesn't replace existing analytics tracking
- **Optimized**: Focused on conditional link evaluation performance
- **Compatible**: Uses same underlying detection libraries
- **Extensible**: Can be extended with additional rule types

## Troubleshooting

### Common Issues

1. **Missing Geo Headers**: Ensure deployment on Vercel or configure alternative geo detection
2. **Timezone Issues**: Verify timezone strings match IANA timezone database
3. **Referrer Blocking**: Some browsers/extensions block referrer headers
4. **Performance**: Monitor evaluation times and implement caching as needed

### Debug Mode

```typescript
// Enable debug logging
process.env.DEBUG_CONDITIONAL_LINKS = 'true'

// This will log evaluation steps and timing information
const result = await evaluator.evaluateLink(link)
```
