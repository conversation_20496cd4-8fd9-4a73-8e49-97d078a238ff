/**
 * Performance monitoring utility for username availability feature
 * Tracks metrics, cache hit rates, and error rates
 */

export interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  success: boolean
  error?: string
  metadata?: Record<string, any>
}

export interface CacheMetric {
  operation: 'hit' | 'miss' | 'set' | 'clear'
  key: string
  timestamp: number
  metadata?: Record<string, any>
}

export interface ErrorMetric {
  operation: string
  error: string
  errorCode?: string
  timestamp: number
  metadata?: Record<string, any>
}

export interface MonitoringStats {
  performance: {
    totalRequests: number
    averageResponseTime: number
    successRate: number
    slowQueries: number
    timeouts: number
  }
  cache: {
    totalOperations: number
    hitRate: number
    missRate: number
    totalHits: number
    totalMisses: number
  }
  errors: {
    totalErrors: number
    errorsByType: Record<string, number>
    errorRate: number
  }
}

class PerformanceMonitor {
  private performanceMetrics: PerformanceMetric[] = []
  private cacheMetrics: CacheMetric[] = []
  private errorMetrics: ErrorMetric[] = []
  private readonly maxMetrics = 1000 // Prevent memory leaks
  private readonly slowQueryThreshold = 1000 // 1 second

  /**
   * Record a performance metric
   */
  recordPerformance(
    operation: string,
    duration: number,
    success: boolean,
    error?: string,
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: Date.now(),
      success,
      error,
      metadata
    }

    this.performanceMetrics.push(metric)
    this.cleanupMetrics()

    // Log slow queries
    if (duration > this.slowQueryThreshold) {
      console.warn(`[PerformanceMonitor] Slow operation detected: ${operation} took ${duration}ms`, {
        operation,
        duration,
        success,
        error,
        metadata
      })
    }

    // Log to external monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService('performance', metric)
    }
  }

  /**
   * Record a cache operation metric
   */
  recordCache(
    operation: 'hit' | 'miss' | 'set' | 'clear',
    key: string,
    metadata?: Record<string, any>
  ): void {
    const metric: CacheMetric = {
      operation,
      key,
      timestamp: Date.now(),
      metadata
    }

    this.cacheMetrics.push(metric)
    this.cleanupMetrics()

    // Log to external monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService('cache', metric)
    }
  }

  /**
   * Record an error metric
   */
  recordError(
    operation: string,
    err: unknown,
    errorCode?: string,
    metadata?: Record<string, any>
  ): void {
    // Normalize error to a string message for consistent logging/metrics
    const error: string = err instanceof Error
      ? err.message
      : typeof err === 'string'
        ? err
        : typeof err === 'object' && err !== null
          ? (() => { try { return JSON.stringify(err) } catch { return '[unserializable-error]' } })()
          : String(err)

    const metric: ErrorMetric = {
      operation,
      error,
      errorCode,
      timestamp: Date.now(),
      metadata
    }

    this.errorMetrics.push(metric)
    this.cleanupMetrics()

    const metaStr = metadata ? (() => { try { return ` metadata=${JSON.stringify(metadata)}` } catch { return ' metadata=[unserializable-metadata]' } })() : ''
    const codeStr = errorCode ? ` code=${errorCode}` : ''
    const logMsg = `[PerformanceMonitor] Error in ${operation}: ${error}${codeStr}${metaStr}`

    // Avoid noisy red errors in development for expected flows; still emit structured warning
    if (process.env.NODE_ENV === 'production') {
      console.error(logMsg)
      this.sendToMonitoringService('error', metric)
    } else {
      console.warn(logMsg)
    }
  }

  /**
   * Get comprehensive monitoring statistics
   */
  getStats(): MonitoringStats {
    const now = Date.now()
    const oneHourAgo = now - (60 * 60 * 1000)

    // Filter metrics from the last hour
    const recentPerformance = this.performanceMetrics.filter(m => m.timestamp > oneHourAgo)
    const recentCache = this.cacheMetrics.filter(m => m.timestamp > oneHourAgo)
    const recentErrors = this.errorMetrics.filter(m => m.timestamp > oneHourAgo)

    // Calculate performance stats
    const totalRequests = recentPerformance.length
    const successfulRequests = recentPerformance.filter(m => m.success).length
    const averageResponseTime = totalRequests > 0 
      ? recentPerformance.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
      : 0
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0
    const slowQueries = recentPerformance.filter(m => m.duration > this.slowQueryThreshold).length
    const timeouts = recentPerformance.filter(m => m.error?.includes('timeout')).length

    // Calculate cache stats
    const cacheHits = recentCache.filter(m => m.operation === 'hit').length
    const cacheMisses = recentCache.filter(m => m.operation === 'miss').length
    const totalCacheOperations = cacheHits + cacheMisses
    const hitRate = totalCacheOperations > 0 ? (cacheHits / totalCacheOperations) * 100 : 0
    const missRate = totalCacheOperations > 0 ? (cacheMisses / totalCacheOperations) * 100 : 0

    // Calculate error stats
    const totalErrors = recentErrors.length
    const errorsByType: Record<string, number> = {}
    recentErrors.forEach(error => {
      const type = error.errorCode || 'unknown'
      errorsByType[type] = (errorsByType[type] || 0) + 1
    })
    const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0

    return {
      performance: {
        totalRequests,
        averageResponseTime: Math.round(averageResponseTime),
        successRate: Math.round(successRate * 100) / 100,
        slowQueries,
        timeouts
      },
      cache: {
        totalOperations: totalCacheOperations,
        hitRate: Math.round(hitRate * 100) / 100,
        missRate: Math.round(missRate * 100) / 100,
        totalHits: cacheHits,
        totalMisses: cacheMisses
      },
      errors: {
        totalErrors,
        errorsByType,
        errorRate: Math.round(errorRate * 100) / 100
      }
    }
  }

  /**
   * Get raw metrics for detailed analysis
   */
  getRawMetrics() {
    return {
      performance: [...this.performanceMetrics],
      cache: [...this.cacheMetrics],
      errors: [...this.errorMetrics]
    }
  }

  /**
   * Clear all metrics (useful for testing)
   */
  clearMetrics(): void {
    this.performanceMetrics = []
    this.cacheMetrics = []
    this.errorMetrics = []
  }

  /**
   * Clean up old metrics to prevent memory leaks
   */
  private cleanupMetrics(): void {
    const now = Date.now()
    const oneHourAgo = now - (60 * 60 * 1000)

    // Keep only recent metrics and limit total count
    this.performanceMetrics = this.performanceMetrics
      .filter(m => m.timestamp > oneHourAgo)
      .slice(-this.maxMetrics)

    this.cacheMetrics = this.cacheMetrics
      .filter(m => m.timestamp > oneHourAgo)
      .slice(-this.maxMetrics)

    this.errorMetrics = this.errorMetrics
      .filter(m => m.timestamp > oneHourAgo)
      .slice(-this.maxMetrics)
  }

  /**
   * Send metrics to external monitoring service
   * In production, this would integrate with services like DataDog, New Relic, etc.
   */
  private sendToMonitoringService(type: string, metric: any): void {
    // This is a placeholder for external monitoring integration
    // In production, you would send to your monitoring service
    console.log(`[MonitoringService] ${type}:`, JSON.stringify(metric))
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Export for testing
export const __testExports = {
  PerformanceMonitor,
  performanceMonitor
}