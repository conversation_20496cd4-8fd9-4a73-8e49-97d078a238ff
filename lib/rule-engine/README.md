# Rule Engine - Priority and Fallback System

A comprehensive rule evaluation system with priority ordering, conflict resolution, performance monitoring, and robust error handling.

## Overview

The Rule Engine provides a sophisticated system for evaluating conditional link rules with the following key features:

- **Priority-based evaluation** with deterministic ordering
- **Comprehensive fallback strategies** for error recovery
- **Real-time performance monitoring** with alerting
- **Automatic conflict detection and resolution**
- **Robust error handling** with retry logic
- **Caching system** for improved performance
- **Validation system** with semantic and security checks

## Quick Start

```typescript
import { EnhancedRuleEvaluator, RuleEngineFactory } from '@/lib/rule-engine'

// Create evaluator
const evaluator = RuleEngineFactory.createEvaluator()

// Evaluate a link
const result = await evaluator.evaluateLink(link, visitorContext, {
  debugMode: true,
  enableCaching: true,
  fallbackStrategy: 'balanced'
})

console.log(`Should show: ${result.shouldShow}`)
console.log(`Evaluation time: ${result.evaluationTime}ms`)
```

## Core Components

### 1. Enhanced Rule Evaluator

The main evaluation engine that processes rules in priority order:

```typescript
import { EnhancedRuleEvaluator } from '@/lib/rule-engine'

const evaluator = new EnhancedRuleEvaluator()

const result = await evaluator.evaluateLink(link, visitorContext, {
  mode: 'standard',
  maxEvaluationTime: 1000,
  debugMode: false,
  fallbackStrategy: 'balanced',
  enableCaching: true,
  cacheTtl: 300000, // 5 minutes
})
```

### 2. Performance Monitor

Tracks evaluation performance and triggers alerts:

```typescript
import { RulePerformanceMonitor } from '@/lib/rule-engine'

const monitor = RulePerformanceMonitor.getInstance()

// Subscribe to alerts
monitor.onAlert((alert) => {
  console.warn(`Performance alert: ${alert.type}`, alert)
})

// Get statistics
const stats = monitor.getPerformanceStats('link-id')
console.log(`Average evaluation time: ${stats.averageEvaluationTime}ms`)
```

### 3. Error Handler

Provides comprehensive error recovery:

```typescript
import { RuleErrorHandler } from '@/lib/rule-engine'

const errorHandler = RuleErrorHandler.getInstance()

// Subscribe to errors
errorHandler.onError((error) => {
  console.error('Rule evaluation error:', error)
})

// Get error statistics
const errorStats = errorHandler.getErrorStats('link-id')
console.log(`Error rate: ${errorStats.errorRate}%`)
```

### 4. Rule Validator

Validates rule configurations:

```typescript
import { RuleValidator } from '@/lib/rule-engine'

const validationResult = RuleValidator.validateRule(ruleConfiguration)

if (!validationResult.isValid) {
  console.error('Validation errors:', validationResult.errors)
}
```

### 5. Conflict Resolver

Detects and resolves rule conflicts:

```typescript
import { RuleConflictResolver } from '@/lib/rule-engine'

const conflictResult = RuleConflictResolver.detectConflicts(link)

if (conflictResult.hasConflicts) {
  const resolutions = RuleConflictResolver.generateResolutions(
    conflictResult.conflicts, 
    link
  )
  
  // Apply automatic resolutions
  const resolvedLink = RuleConflictResolver.applyResolutions(
    resolutions.filter(r => r.automatic), 
    link
  )
}
```

## Rule Types

### Referrer Rules
Match based on referring website:

```typescript
const referrerRule: ReferrerRule = {
  type: 'referrer',
  domains: ['google.com', 'bing.com'],
  matchType: 'exact', // 'exact' | 'contains' | 'regex'
  caseSensitive: false,
  excludeDomains: ['ads.google.com']
}
```

### Location Rules
Match based on visitor location:

```typescript
const locationRule: LocationRule = {
  type: 'location',
  countries: ['US', 'CA'],
  regions: ['California', 'New York'],
  cities: ['San Francisco', 'New York City'],
  timezones: ['America/Los_Angeles'],
  excludeCountries: ['CN']
}
```

### Device Rules
Match based on device characteristics:

```typescript
const deviceRule: DeviceRule = {
  type: 'device',
  deviceTypes: ['mobile', 'tablet'],
  platforms: ['iOS', 'Android'],
  browsers: ['Chrome', 'Safari'],
  excludeDeviceTypes: ['desktop']
}
```

### Time Rules
Match based on time and date:

```typescript
const timeRule: TimeRule = {
  type: 'time',
  daysOfWeek: [1, 2, 3, 4, 5], // Monday-Friday
  startTime: '09:00',
  endTime: '17:00',
  timezone: 'America/Los_Angeles',
  dateRange: {
    start: new Date('2024-01-01'),
    end: new Date('2024-12-31')
  }
}
```

### Schedule Rules
Match based on link schedule:

```typescript
const scheduleRule: ScheduleRule = {
  type: 'schedule',
  scheduleStart: new Date('2024-01-01T00:00:00Z'),
  scheduleEnd: new Date('2024-12-31T23:59:59Z'),
  timezone: 'America/Los_Angeles',
  recurring: {
    pattern: 'weekly',
    interval: 2, // Every 2 weeks
    endDate: new Date('2024-12-31')
  }
}
```

## Configuration

### Performance Thresholds

```typescript
import { PERFORMANCE_THRESHOLDS } from '@/lib/rule-engine'

// Default thresholds
PERFORMANCE_THRESHOLDS.MAX_EVALUATION_TIME // 10000ms
PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING // 100ms
PERFORMANCE_THRESHOLDS.MAX_CONDITIONS_PER_LINK // 100
```

### Fallback Strategies

```typescript
import { FALLBACK_STRATEGIES } from '@/lib/rule-engine'

// Available strategies
FALLBACK_STRATEGIES.conservative // Hide on errors
FALLBACK_STRATEGIES.balanced     // Use default behavior
FALLBACK_STRATEGIES.aggressive   // Show on errors
FALLBACK_STRATEGIES.performance  // Fast fallback with caching
```

### Feature Flags

```typescript
import { FEATURE_FLAGS } from '@/lib/rule-engine'

// Control feature rollout
FEATURE_FLAGS.ENHANCED_EVALUATION // true
FEATURE_FLAGS.PERFORMANCE_MONITORING // true
FEATURE_FLAGS.CONFLICT_DETECTION // true
FEATURE_FLAGS.CACHING_ENABLED // true
```

## Utilities

### Rule Engine Utils

```typescript
import { RuleEngineUtils } from '@/lib/rule-engine'

// Validate a rule
const validation = RuleEngineUtils.validateRule(rule)

// Detect conflicts
const conflicts = RuleEngineUtils.detectConflicts(link)

// Calculate complexity
const complexity = RuleEngineUtils.calculateComplexity(conditions)

// Optimize priorities
const optimized = RuleEngineUtils.optimizePriorities(conditions)

// Get recommended strategy
const strategy = RuleEngineUtils.getRecommendedFallbackStrategy(link)

// Generate health report
const health = RuleEngineUtils.generateHealthReport(link)
```

## Database Schema

The system requires enhanced indexes for optimal performance:

```sql
-- Enhanced indexes for LinkCondition table
CREATE INDEX "LinkCondition_linkId_priority_isActive_idx" 
ON "LinkCondition"("linkId", "priority" DESC, "isActive");

CREATE INDEX "LinkCondition_priority_createdAt_idx" 
ON "LinkCondition"("priority" DESC, "createdAt" DESC);
```

## Testing

Run the test suite:

```bash
bun test lib/rule-engine/__tests__/
```

Example test:

```typescript
import { EnhancedRuleEvaluator } from '@/lib/rule-engine'

describe('Rule Evaluation', () => {
  it('should evaluate conditions in priority order', async () => {
    const evaluator = new EnhancedRuleEvaluator()
    const result = await evaluator.evaluateLink(link, context)
    
    expect(result.shouldShow).toBe(true)
    expect(result.matchedConditions).toContain('high-priority-condition')
  })
})
```

## Integration with Existing System

To integrate with the existing conditional rule evaluator:

1. **Replace existing evaluator calls**:
   ```typescript
   // Old
   const result = await evaluateConditionalRules(link, context)
   
   // New
   const evaluator = new EnhancedRuleEvaluator()
   const result = await evaluator.evaluateLink(link, context)
   ```

2. **Update database schema** with enhanced indexes

3. **Configure monitoring** and error handling

4. **Gradually enable features** using feature flags

## Performance Considerations

- **Caching**: Enable caching for frequently evaluated links
- **Priorities**: Use unique priorities to avoid conflicts
- **Complexity**: Keep rule complexity low for better performance
- **Monitoring**: Set up alerts for slow evaluations
- **Cleanup**: Regularly clean up old performance data

## Error Handling

The system provides comprehensive error handling:

- **Validation errors**: Invalid rule configurations
- **Network errors**: External service failures
- **Timeout errors**: Evaluation taking too long
- **System errors**: Memory or resource issues
- **Unknown errors**: Unexpected failures

Each error type has appropriate fallback strategies and retry logic.

## Monitoring and Alerting

Set up monitoring for:

- **Evaluation performance**: Average, P95, P99 response times
- **Error rates**: Percentage of failed evaluations
- **Cache performance**: Hit rates and miss patterns
- **Memory usage**: Heap usage during evaluation
- **Rule complexity**: Complexity scores and warnings

## Best Practices

1. **Use unique priorities** for deterministic evaluation
2. **Keep rules simple** to maintain performance
3. **Enable caching** for frequently accessed links
4. **Monitor performance** regularly
5. **Validate rules** before deployment
6. **Handle conflicts** proactively
7. **Use appropriate fallback strategies**
8. **Test thoroughly** with realistic data

## Support

For issues or questions:

1. Check the test suite for examples
2. Review the type definitions for API details
3. Use debug mode for detailed evaluation logs
4. Monitor performance metrics for optimization opportunities
