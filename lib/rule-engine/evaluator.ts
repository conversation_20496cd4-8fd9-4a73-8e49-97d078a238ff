
import { LinkWithConditions, LinkCondition as PrismaLinkCondition } from '../types';
import { VisitorContext, LinkCondition, RuleEvaluationResult, ReferrerCondition, LocationCondition, DeviceCondition, TimeCondition } from './conditions';
import { toZonedTime, format } from 'date-fns-tz';

export class RuleEvaluator {
  evaluate(link: LinkWithConditions, context: VisitorContext): RuleEvaluationResult {
    // 1. Evaluate schedule
    if (link.isScheduled) {
      const now = context.now || new Date();
      const start = link.scheduleStart;
      const end = link.scheduleEnd;

      if (start && now < start) {
        return { shouldShow: false };
      }
      if (end && now > end) {
        return { shouldShow: false };
      }
    }

    // 2. Evaluate conditions if they exist
    if (!link.hasConditions || !link.conditions || link.conditions.length === 0) {
      return { shouldShow: true };
    }

    const conditions = link.conditions.sort((a, b) => a.priority - b.priority);

    for (const condition of conditions) {
      if (this.isConditionMet(condition, context)) {
        // For now, assume the action is to show the link.
        // A more advanced implementation would use `condition.action`.
        const parsedCondition = { ...condition.rules as object, type: condition.type } as LinkCondition;
        return { shouldShow: true, matchedCondition: parsedCondition };
      }
    }

    // 3. Default behavior if no conditions are met
    return { shouldShow: link.defaultBehavior === 'show' };
  }

  private isConditionMet(condition: PrismaLinkCondition, context: VisitorContext): boolean {
    if (!condition.isActive) {
      return false;
    }
    
    const rules = condition.rules as any;

    switch (condition.type) {
      case 'referrer':
        return this.evaluateReferrer(rules, context);
      case 'location':
        return this.evaluateLocation(rules, context);
      case 'device':
        return this.evaluateDevice(rules, context);
      case 'time':
        return this.evaluateTime(rules, context);
      default:
        return false;
    }
  }

  private evaluateReferrer(rules: ReferrerCondition, context: VisitorContext): boolean {
    const referrer = context.referrer;
    if (!referrer) return false;

    switch (rules.match) {
      case 'exact':
        return referrer === rules.value;
      case 'contains':
        return referrer.includes(rules.value);
      case 'regex':
        try {
          return new RegExp(rules.value).test(referrer);
        } catch (e) {
          console.error("Invalid regex in referrer condition:", e);
          return false;
        }
      default:
        return false;
    }
  }

  private evaluateLocation(rules: LocationCondition, context: VisitorContext): boolean {
    const location = context.location;
    if (!location) return false;

    switch (rules.match) {
      case 'country':
        return !!location.country && location.country === rules.value;
      case 'region':
        return !!location.region && location.region === rules.value;
      case 'city':
        return !!location.city && location.city === rules.value;
      default:
        return false;
    }
  }

  private evaluateDevice(rules: DeviceCondition, context: VisitorContext): boolean {
    const device = context.device;
    if (!device) return false;

    switch (rules.match) {
      case 'type':
        return !!device.type && device.type === rules.value;
      case 'platform':
        return !!device.platform && device.platform === rules.value;
      case 'browser':
        return !!device.browser && device.browser === rules.value;
      default:
        return false;
    }
  }

  private evaluateTime(rules: TimeCondition, context: VisitorContext): boolean {
    const timezone = context.timezone || 'UTC';
    const now = context.now || new Date();
    const zonedNow = toZonedTime(now, timezone);
    
    if (rules.dayOfWeek !== undefined && zonedNow.getDay() !== rules.dayOfWeek) {
      return false;
    }

    if (rules.startTime || rules.endTime) {
        const currentTime = format(zonedNow, 'HH:mm', { timeZone: timezone });
        
        if (rules.startTime && currentTime < rules.startTime) {
            return false;
        }
        if (rules.endTime && currentTime > rules.endTime) {
            return false;
        }
    }

    return true;
  }
}


