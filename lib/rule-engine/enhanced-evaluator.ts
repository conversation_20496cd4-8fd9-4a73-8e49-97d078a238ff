/**
 * Enhanced Rule Evaluator
 * 
 * Main rule evaluation engine with priority system, fallback handling, and performance monitoring
 */

import { v4 as uuidv4 } from 'uuid'
import type {
  RuleEvaluationContext,
  RuleEvaluationResult,
  ConditionalLinkWithConditions,
  EnhancedLinkCondition,
  ConditionAction,
  PerformanceMetrics,
} from './types'
import type { ConditionalVisitorContext } from '../utils/conditional-visitor-context'
import { RulePerformanceMonitor } from './performance-monitor'
import { RuleErrorHandler } from './error-handler'
import { RuleValidator } from './rule-validator'
import { RuleConflictResolver } from './conflict-resolver'
import {
  PERFORMANCE_THRESHOLDS,
  FALLBACK_STRATEGIES,
  EVALUATION_MODES,
  PRIORITY_SYSTEM,
  DEFAULTS,
  FEATURE_FLAGS,
} from './constants'

/**
 * Evaluation options for customizing behavior
 */
export interface EvaluationOptions {
  /** Evaluation mode */
  mode?: keyof typeof EVALUATION_MODES
  
  /** Maximum evaluation time in milliseconds */
  maxEvaluationTime?: number
  
  /** Enable debug mode for detailed logging */
  debugMode?: boolean
  
  /** Fallback strategy to use */
  fallbackStrategy?: keyof typeof FALLBACK_STRATEGIES
  
  /** Enable caching */
  enableCaching?: boolean
  
  /** Cache TTL in milliseconds */
  cacheTtl?: number
  
  /** Skip validation (for performance) */
  skipValidation?: boolean
  
  /** Skip conflict detection */
  skipConflictDetection?: boolean
}

/**
 * Enhanced rule evaluator class
 */
export class EnhancedRuleEvaluator {
  private performanceMonitor: RulePerformanceMonitor
  private errorHandler: RuleErrorHandler
  private evaluationCache: Map<string, RuleEvaluationResult> = new Map()

  constructor() {
    this.performanceMonitor = RulePerformanceMonitor.getInstance()
    this.errorHandler = RuleErrorHandler.getInstance()
  }

  /**
   * Evaluate rules for a link with comprehensive error handling and performance monitoring
   */
  public async evaluateLink(
    link: ConditionalLinkWithConditions,
    visitorContext: ConditionalVisitorContext,
    options: EvaluationOptions = {}
  ): Promise<RuleEvaluationResult> {
    const evaluationId = uuidv4()
    const startTime = performance.now()
    
    // Create evaluation context
    const context: RuleEvaluationContext = {
      evaluationId,
      link,
      visitorContext,
      startTime,
      currentTime: new Date(),
      cacheKey: this.generateCacheKey(link.id, visitorContext),
      debugMode: options.debugMode || DEFAULTS.DEBUG_MODE,
    }

    try {
      // Check cache first if enabled
      if (options.enableCaching !== false && FEATURE_FLAGS.CACHING_ENABLED) {
        const cachedResult = this.getCachedResult(context.cacheKey!)
        if (cachedResult) {
          this.performanceMonitor.recordCacheHit(link.id)
          return {
            ...cachedResult,
            metadata: {
              ...cachedResult.metadata,
              strategy: 'cache',
            },
          }
        }
        this.performanceMonitor.recordCacheMiss(link.id)
      }

      // Validate link configuration if not skipped
      if (!options.skipValidation && FEATURE_FLAGS.ENHANCED_EVALUATION) {
        await this.validateLinkConfiguration(link)
      }

      // Detect conflicts if not skipped
      if (!options.skipConflictDetection && FEATURE_FLAGS.CONFLICT_DETECTION) {
        await this.detectAndResolveConflicts(link)
      }

      // Perform rule evaluation
      const result = await this.performEvaluation(context, options)

      // Record performance metrics
      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const metrics = this.performanceMonitor.endTiming(context, result, startTime)
        result.performanceMetrics = metrics
      }

      // Cache result if enabled
      if (options.enableCaching !== false && FEATURE_FLAGS.CACHING_ENABLED && context.cacheKey) {
        this.cacheResult(context.cacheKey, result, options.cacheTtl)
      }

      return result

    } catch (error) {
      // Handle evaluation error
      this.performanceMonitor.recordError(link.id, 'evaluation_error')
      
      const fallbackStrategy = FALLBACK_STRATEGIES[options.fallbackStrategy || 'balanced']
      return await this.errorHandler.handleEvaluationError(
        error as Error,
        context,
        fallbackStrategy
      )
    }
  }

  /**
   * Evaluate multiple links efficiently
   */
  public async evaluateLinks(
    links: ConditionalLinkWithConditions[],
    visitorContext: ConditionalVisitorContext,
    options: EvaluationOptions = {}
  ): Promise<RuleEvaluationResult[]> {
    const evaluationPromises = links.map(link => 
      this.evaluateLink(link, visitorContext, options)
    )

    return Promise.all(evaluationPromises)
  }

  /**
   * Pre-validate a link configuration
   */
  public async validateLinkConfiguration(
    link: ConditionalLinkWithConditions
  ): Promise<void> {
    if (!FEATURE_FLAGS.ENHANCED_EVALUATION) {
      return
    }

    const validationResult = RuleValidator.validateConditions(link.conditions)
    if (!validationResult.isValid) {
      throw new Error(`Link validation failed: ${validationResult.errors.join(', ')}`)
    }

    if (validationResult.warnings.length > 0 && process.env.NODE_ENV === 'development') {
      console.warn(`[EnhancedRuleEvaluator] Link validation warnings for ${link.id}:`, validationResult.warnings)
    }
  }

  /**
   * Detect and resolve rule conflicts
   */
  public async detectAndResolveConflicts(
    link: ConditionalLinkWithConditions
  ): Promise<ConditionalLinkWithConditions> {
    if (!FEATURE_FLAGS.CONFLICT_DETECTION) {
      return link
    }

    const conflictResult = RuleConflictResolver.detectConflicts(link)
    
    if (conflictResult.hasConflicts) {
      const resolutions = RuleConflictResolver.generateResolutions(conflictResult.conflicts, link)
      const automaticResolutions = resolutions.filter(r => r.automatic)
      
      if (automaticResolutions.length > 0) {
        const resolvedLink = RuleConflictResolver.applyResolutions(automaticResolutions, link)
        
        if (process.env.NODE_ENV === 'development') {
          console.info(`[EnhancedRuleEvaluator] Applied ${automaticResolutions.length} automatic conflict resolutions for link ${link.id}`)
        }
        
        return resolvedLink
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.warn(`[EnhancedRuleEvaluator] Unresolved conflicts detected for link ${link.id}:`, conflictResult.conflicts)
      }
    }

    return link
  }

  /**
   * Clear evaluation cache
   */
  public clearCache(): void {
    this.evaluationCache.clear()
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number
    hitRate: number
  } {
    return {
      size: this.evaluationCache.size,
      hitRate: this.performanceMonitor.getPerformanceStats().cacheHitRate,
    }
  }

  /**
   * Perform the actual rule evaluation
   */
  private async performEvaluation(
    context: RuleEvaluationContext,
    options: EvaluationOptions
  ): Promise<RuleEvaluationResult> {
    const { link, visitorContext } = context
    const maxTime = options.maxEvaluationTime || PERFORMANCE_THRESHOLDS.MAX_EVALUATION_TIME

    // Set up timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Rule evaluation timeout')), maxTime)
    })

    // Perform evaluation with timeout
    const evaluationPromise = this.evaluateConditions(context, options)

    try {
      return await Promise.race([evaluationPromise, timeoutPromise])
    } catch (error) {
      if (error instanceof Error && error.message === 'Rule evaluation timeout') {
        this.performanceMonitor.recordError(link.id, 'timeout_error')
      }
      throw error
    }
  }

  /**
   * Evaluate conditions in priority order
   */
  private async evaluateConditions(
    context: RuleEvaluationContext,
    options: EvaluationOptions
  ): Promise<RuleEvaluationResult> {
    const { link, visitorContext } = context
    const activeConditions = link.conditions.filter(c => c.isActive)

    // Sort conditions by priority (highest first)
    const sortedConditions = [...activeConditions].sort((a, b) => {
      // Schedule conditions always have highest priority
      if (a.type === 'schedule' && b.type !== 'schedule') return -1
      if (b.type === 'schedule' && a.type !== 'schedule') return 1
      
      // Then by priority value
      return b.priority - a.priority
    })

    const matchedConditions: string[] = []
    const conditionEvaluationTimes: Record<string, number> = {}
    const skippedConditions: string[] = []
    const warnings: string[] = []

    // Evaluate conditions in priority order
    for (const condition of sortedConditions) {
      const conditionStartTime = performance.now()

      try {
        const matches = await this.evaluateCondition(condition, visitorContext, context)
        
        conditionEvaluationTimes[condition.id] = performance.now() - conditionStartTime

        if (matches) {
          matchedConditions.push(condition.id)
          
          // Apply the action from the first matching condition
          const result = this.createEvaluationResult(
            context,
            condition,
            matchedConditions,
            conditionEvaluationTimes,
            skippedConditions,
            warnings
          )

          return result
        }
      } catch (error) {
        conditionEvaluationTimes[condition.id] = performance.now() - conditionStartTime
        warnings.push(`Error evaluating condition ${condition.id}: ${(error as Error).message}`)
        
        // Continue to next condition on error
        continue
      }
    }

    // No conditions matched, use default behavior
    return this.createDefaultResult(
      context,
      conditionEvaluationTimes,
      skippedConditions,
      warnings
    )
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(
    condition: EnhancedLinkCondition,
    visitorContext: ConditionalVisitorContext,
    context: RuleEvaluationContext
  ): Promise<boolean> {
    // This would contain the actual condition evaluation logic
    // For now, we'll use a simplified version based on the existing evaluator
    
    const { rules } = condition

    switch (rules.type) {
      case 'referrer':
        return this.evaluateReferrerRule(rules, visitorContext)
      case 'location':
        return this.evaluateLocationRule(rules, visitorContext)
      case 'device':
        return this.evaluateDeviceRule(rules, visitorContext)
      case 'time':
        return this.evaluateTimeRule(rules, context.currentTime)
      case 'schedule':
        return this.evaluateScheduleRule(rules, context.currentTime)
      default:
        return false
    }
  }

  /**
   * Create evaluation result for matched condition
   */
  private createEvaluationResult(
    context: RuleEvaluationContext,
    matchedCondition: EnhancedLinkCondition,
    matchedConditions: string[],
    conditionEvaluationTimes: Record<string, number>,
    skippedConditions: string[],
    warnings: string[]
  ): RuleEvaluationResult {
    const { link } = context
    const action = matchedCondition.action

    return {
      linkId: link.id,
      shouldShow: action.type === 'show',
      matchedConditions,
      appliedAction: action,
      evaluationTime: performance.now() - context.startTime,
      fallbackUsed: false,
      performanceMetrics: {
        totalEvaluationTime: performance.now() - context.startTime,
        conditionEvaluationTimes,
        visitorContextParsingTime: 0,
        databaseQueryTime: 0,
        cacheHitRate: 0,
        conditionsEvaluated: matchedConditions.length,
      },
      effectiveTitle: action.alternateTitle || link.title,
      effectiveIcon: action.alternateIcon || link.icon,
      effectiveUrl: action.type === 'redirect' ? (action.value || link.url) : link.url,
      metadata: {
        strategy: 'priority',
        skippedConditions,
        warnings,
        debugInfo: context.debugMode ? {
          evaluationId: context.evaluationId,
          matchedConditionId: matchedCondition.id,
          conditionType: matchedCondition.type,
          conditionPriority: matchedCondition.priority,
        } : undefined,
      },
    }
  }

  /**
   * Create default result when no conditions match
   */
  private createDefaultResult(
    context: RuleEvaluationContext,
    conditionEvaluationTimes: Record<string, number>,
    skippedConditions: string[],
    warnings: string[]
  ): RuleEvaluationResult {
    const { link } = context
    const defaultAction: ConditionAction = { type: link.defaultBehavior }

    return {
      linkId: link.id,
      shouldShow: link.defaultBehavior === 'show',
      matchedConditions: [],
      appliedAction: defaultAction,
      evaluationTime: performance.now() - context.startTime,
      fallbackUsed: false,
      performanceMetrics: {
        totalEvaluationTime: performance.now() - context.startTime,
        conditionEvaluationTimes,
        visitorContextParsingTime: 0,
        databaseQueryTime: 0,
        cacheHitRate: 0,
        conditionsEvaluated: Object.keys(conditionEvaluationTimes).length,
      },
      effectiveTitle: link.title,
      effectiveIcon: link.icon,
      effectiveUrl: link.url,
      metadata: {
        strategy: 'default',
        skippedConditions,
        warnings,
        debugInfo: context.debugMode ? {
          evaluationId: context.evaluationId,
          defaultBehavior: link.defaultBehavior,
        } : undefined,
      },
    }
  }

  /**
   * Generate cache key for evaluation result
   */
  private generateCacheKey(linkId: string, visitorContext: ConditionalVisitorContext): string {
    const contextHash = this.hashVisitorContext(visitorContext)
    return `${linkId}:${contextHash}`
  }

  /**
   * Hash visitor context for cache key
   */
  private hashVisitorContext(context: ConditionalVisitorContext): string {
    const relevantData = {
      device: context.device.type,
      country: context.location.country,
      referrerDomain: context.referrer.domain,
      // Add other relevant fields as needed
    }
    
    return Buffer.from(JSON.stringify(relevantData)).toString('base64').slice(0, 16)
  }

  /**
   * Get cached evaluation result
   */
  private getCachedResult(cacheKey: string): RuleEvaluationResult | null {
    return this.evaluationCache.get(cacheKey) || null
  }

  /**
   * Cache evaluation result
   */
  private cacheResult(
    cacheKey: string,
    result: RuleEvaluationResult,
    ttl: number = 300000 // 5 minutes
  ): void {
    this.evaluationCache.set(cacheKey, result)
    
    // Set expiration
    setTimeout(() => {
      this.evaluationCache.delete(cacheKey)
    }, ttl)
  }

  // Condition evaluation methods
  private async evaluateReferrerRule(rules: any, context: ConditionalVisitorContext): Promise<boolean> {
    const referrerDomain = context.referrer.domain
    if (!referrerDomain) return false

    const { domains, matchType, caseSensitive, excludeDomains } = rules

    // Check exclude list first
    if (excludeDomains && excludeDomains.length > 0) {
      const excluded = excludeDomains.some((domain: string) =>
        this.matchDomain(referrerDomain, domain, matchType, caseSensitive)
      )
      if (excluded) return false
    }

    // Check include list
    return domains.some((domain: string) =>
      this.matchDomain(referrerDomain, domain, matchType, caseSensitive)
    )
  }

  private async evaluateLocationRule(rules: any, context: ConditionalVisitorContext): Promise<boolean> {
    const { country, region, city, timezone } = context.location

    // Check exclude lists first
    if (rules.excludeCountries && rules.excludeCountries.includes(country)) return false
    if (rules.excludeRegions && rules.excludeRegions.includes(region)) return false
    if (rules.excludeCities && rules.excludeCities.includes(city)) return false

    // Check include lists (any match is sufficient)
    let hasMatch = false

    if (rules.countries && rules.countries.length > 0) {
      hasMatch = hasMatch || rules.countries.includes(country)
    }

    if (rules.regions && rules.regions.length > 0) {
      hasMatch = hasMatch || rules.regions.includes(region)
    }

    if (rules.cities && rules.cities.length > 0) {
      hasMatch = hasMatch || rules.cities.includes(city)
    }

    if (rules.timezones && rules.timezones.length > 0) {
      hasMatch = hasMatch || rules.timezones.includes(timezone)
    }

    // If no include criteria specified, default to true (unless excluded)
    const hasIncludeCriteria = !!(rules.countries?.length || rules.regions?.length || rules.cities?.length || rules.timezones?.length)
    return hasIncludeCriteria ? hasMatch : true
  }

  private async evaluateDeviceRule(rules: any, context: ConditionalVisitorContext): Promise<boolean> {
    const { type: deviceType, platform, browser } = context.device

    // Check exclude lists first
    if (rules.excludeDeviceTypes && rules.excludeDeviceTypes.includes(deviceType)) return false
    if (rules.excludePlatforms && rules.excludePlatforms.includes(platform)) return false
    if (rules.excludeBrowsers && rules.excludeBrowsers.includes(browser)) return false

    // Check include lists (any match is sufficient)
    let hasMatch = false

    if (rules.deviceTypes && rules.deviceTypes.length > 0) {
      hasMatch = hasMatch || rules.deviceTypes.includes(deviceType)
    }

    if (rules.platforms && rules.platforms.length > 0) {
      hasMatch = hasMatch || rules.platforms.includes(platform)
    }

    if (rules.browsers && rules.browsers.length > 0) {
      hasMatch = hasMatch || rules.browsers.includes(browser)
    }

    // If no include criteria specified, default to true (unless excluded)
    const hasIncludeCriteria = !!(rules.deviceTypes?.length || rules.platforms?.length || rules.browsers?.length)
    return hasIncludeCriteria ? hasMatch : true
  }

  private async evaluateTimeRule(rules: any, currentTime: Date): Promise<boolean> {
    const now = new Date(currentTime)

    // Check day of week
    if (rules.daysOfWeek && rules.daysOfWeek.length > 0) {
      const dayOfWeek = now.getDay() // 0 = Sunday
      if (!rules.daysOfWeek.includes(dayOfWeek)) return false
    }

    // Check time range
    if (rules.startTime || rules.endTime) {
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      if (rules.startTime) {
        const startMinutes = this.parseTimeToMinutes(rules.startTime)
        if (currentMinutes < startMinutes) return false
      }

      if (rules.endTime) {
        const endMinutes = this.parseTimeToMinutes(rules.endTime)
        if (currentMinutes > endMinutes) return false
      }
    }

    // Check date range
    if (rules.dateRange) {
      const currentDate = now.getTime()
      const startDate = new Date(rules.dateRange.start).getTime()
      const endDate = new Date(rules.dateRange.end).getTime()

      if (currentDate < startDate || currentDate > endDate) return false
    }

    return true
  }

  private async evaluateScheduleRule(rules: any, currentTime: Date): Promise<boolean> {
    const now = new Date(currentTime)
    const startTime = new Date(rules.scheduleStart).getTime()
    const currentTimeMs = now.getTime()

    // Check if we're past the start time
    if (currentTimeMs < startTime) return false

    // Check if we're past the end time (if specified)
    if (rules.scheduleEnd) {
      const endTime = new Date(rules.scheduleEnd).getTime()
      if (currentTimeMs > endTime) return false
    }

    // Handle recurring patterns
    if (rules.recurring) {
      const { pattern, interval, endDate } = rules.recurring

      // Check if recurring period has ended
      if (endDate && currentTimeMs > new Date(endDate).getTime()) return false

      const timeSinceStart = currentTimeMs - startTime

      switch (pattern) {
        case 'daily':
          const daysSinceStart = Math.floor(timeSinceStart / (24 * 60 * 60 * 1000))
          return daysSinceStart % interval === 0
        case 'weekly':
          const weeksSinceStart = Math.floor(timeSinceStart / (7 * 24 * 60 * 60 * 1000))
          return weeksSinceStart % interval === 0
        case 'monthly':
          // Simplified monthly calculation
          const monthsSinceStart = Math.floor(timeSinceStart / (30 * 24 * 60 * 60 * 1000))
          return monthsSinceStart % interval === 0
      }
    }

    return true
  }

  /**
   * Helper method to match domains
   */
  private matchDomain(referrerDomain: string, ruleDomain: string, matchType: string, caseSensitive: boolean): boolean {
    const referrer = caseSensitive ? referrerDomain : referrerDomain.toLowerCase()
    const rule = caseSensitive ? ruleDomain : ruleDomain.toLowerCase()

    switch (matchType) {
      case 'exact':
        return referrer === rule
      case 'contains':
        return referrer.includes(rule)
      case 'regex':
        try {
          const regex = new RegExp(rule, caseSensitive ? '' : 'i')
          return regex.test(referrer)
        } catch {
          return false
        }
      default:
        return false
    }
  }

  /**
   * Helper method to parse time string to minutes
   */
  private parseTimeToMinutes(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return hours * 60 + minutes
  }
}
