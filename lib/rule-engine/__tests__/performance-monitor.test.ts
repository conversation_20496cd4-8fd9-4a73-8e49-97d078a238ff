/**
 * Performance Monitor Tests
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { RulePerformanceMonitor } from '../performance-monitor'
import type { 
  RuleEvaluationContext, 
  RuleEvaluationResult,
  PerformanceAlert 
} from '../types'

// Mock performance.now
const mockPerformanceNow = jest.fn()
Object.defineProperty(global, 'performance', {
  value: { now: mockPerformanceNow },
  writable: true,
})

describe('RulePerformanceMonitor', () => {
  let monitor: RulePerformanceMonitor
  let mockContext: RuleEvaluationContext
  let mockResult: RuleEvaluationResult

  beforeEach(() => {
    monitor = RulePerformanceMonitor.getInstance()
    monitor.clearData()

    // Mock performance.now to return predictable values
    let timeCounter = 0
    mockPerformanceNow.mockImplementation(() => timeCounter += 10)

    // Create mock context
    mockContext = {
      evaluationId: 'eval-123',
      link: {
        id: 'link-1',
        title: 'Test Link',
        url: 'https://example.com',
        isVisible: true,
        order: 1,
        isScheduled: false,
        hasConditions: true,
        conditions: [],
        defaultBehavior: 'show',
      },
      visitorContext: {} as any,
      startTime: 0,
      currentTime: new Date(),
      debugMode: false,
    }

    // Create mock result
    mockResult = {
      linkId: 'link-1',
      shouldShow: true,
      matchedConditions: ['condition-1'],
      appliedAction: { type: 'show' },
      evaluationTime: 50,
      fallbackUsed: false,
      performanceMetrics: {
        totalEvaluationTime: 50,
        conditionEvaluationTimes: { 'condition-1': 25 },
        visitorContextParsingTime: 5,
        databaseQueryTime: 10,
        cacheHitRate: 80,
        conditionsEvaluated: 1,
      },
      effectiveTitle: 'Test Link',
      effectiveUrl: 'https://example.com',
      metadata: {
        strategy: 'priority',
        skippedConditions: [],
        warnings: [],
      },
    }
  })

  afterEach(() => {
    monitor.clearData()
    jest.clearAllMocks()
  })

  describe('timing operations', () => {
    it('should start and end timing correctly', () => {
      const startTime = monitor.startTiming('eval-123')
      expect(startTime).toBe(10) // Based on our mock

      const metrics = monitor.endTiming(mockContext, mockResult, startTime)
      
      expect(metrics).toMatchObject({
        totalEvaluationTime: expect.any(Number),
        conditionEvaluationTimes: expect.any(Object),
        cacheHitRate: expect.any(Number),
        conditionsEvaluated: 1,
      })
    })

    it('should record performance data points', () => {
      const startTime = monitor.startTiming('eval-123')
      monitor.endTiming(mockContext, mockResult, startTime)

      const recentData = monitor.getRecentData('link-1', 60)
      expect(recentData).toHaveLength(1)
      expect(recentData[0]).toMatchObject({
        linkId: 'link-1',
        success: true,
        cacheHit: false,
      })
    })
  })

  describe('cache tracking', () => {
    it('should record cache hits and misses', () => {
      monitor.recordCacheHit('link-1')
      monitor.recordCacheHit('link-1')
      monitor.recordCacheMiss('link-1')

      // Cache hit rate should be 66.67% (2 hits out of 3 total)
      const startTime = monitor.startTiming('eval-123')
      const metrics = monitor.endTiming(mockContext, mockResult, startTime)
      
      expect(metrics.cacheHitRate).toBeCloseTo(66.67, 1)
    })
  })

  describe('error tracking', () => {
    it('should record errors correctly', () => {
      monitor.recordError('link-1', 'validation_error', 'condition-1')
      monitor.recordError('link-1', 'timeout_error')

      const stats = monitor.getPerformanceStats('link-1')
      // Error rate calculation depends on having evaluation data
      expect(stats).toBeDefined()
    })

    it('should trigger error rate alerts', (done) => {
      const alertCallback = jest.fn((alert: PerformanceAlert) => {
        expect(alert.type).toBe('high_error_rate')
        expect(alert.linkId).toBe('link-1')
        expect(alert.severity).toBe('critical')
        done()
      })

      monitor.onAlert(alertCallback)

      // Simulate high error rate by recording many errors
      for (let i = 0; i < 10; i++) {
        monitor.recordError('link-1', 'validation_error')
        
        // Also need to record some data points to calculate error rate
        const startTime = monitor.startTiming(`eval-${i}`)
        const errorResult = {
          ...mockResult,
          fallbackUsed: true,
          errorDetails: 'Validation error',
        }
        monitor.endTiming(mockContext, errorResult, startTime)
      }
    })
  })

  describe('performance statistics', () => {
    beforeEach(() => {
      // Add some test data
      for (let i = 0; i < 5; i++) {
        const startTime = monitor.startTiming(`eval-${i}`)
        const result = {
          ...mockResult,
          evaluationTime: 50 + i * 10, // Varying evaluation times
        }
        monitor.endTiming(mockContext, result, startTime)
      }
    })

    it('should calculate performance statistics correctly', () => {
      const stats = monitor.getPerformanceStats('link-1')

      expect(stats).toMatchObject({
        totalEvaluations: 5,
        averageEvaluationTime: expect.any(Number),
        p50EvaluationTime: expect.any(Number),
        p95EvaluationTime: expect.any(Number),
        p99EvaluationTime: expect.any(Number),
        errorRate: expect.any(Number),
        cacheHitRate: expect.any(Number),
        slowEvaluationCount: expect.any(Number),
        timeoutCount: expect.any(Number),
      })

      expect(stats.totalEvaluations).toBe(5)
      expect(stats.averageEvaluationTime).toBeGreaterThan(0)
    })

    it('should filter statistics by link ID', () => {
      // Add data for another link
      const otherContext = { ...mockContext, link: { ...mockContext.link, id: 'link-2' } }
      const startTime = monitor.startTiming('eval-other')
      monitor.endTiming(otherContext, { ...mockResult, linkId: 'link-2' }, startTime)

      const link1Stats = monitor.getPerformanceStats('link-1')
      const link2Stats = monitor.getPerformanceStats('link-2')
      const allStats = monitor.getPerformanceStats()

      expect(link1Stats.totalEvaluations).toBe(5)
      expect(link2Stats.totalEvaluations).toBe(1)
      expect(allStats.totalEvaluations).toBe(6)
    })

    it('should handle empty statistics gracefully', () => {
      monitor.clearData()
      const stats = monitor.getPerformanceStats('nonexistent-link')

      expect(stats).toMatchObject({
        totalEvaluations: 0,
        averageEvaluationTime: 0,
        p50EvaluationTime: 0,
        p95EvaluationTime: 0,
        p99EvaluationTime: 0,
        errorRate: 0,
        cacheHitRate: 0,
        slowEvaluationCount: 0,
        timeoutCount: 0,
      })
    })
  })

  describe('alert system', () => {
    it('should trigger slow evaluation alerts', (done) => {
      const alertCallback = jest.fn((alert: PerformanceAlert) => {
        expect(alert.type).toBe('slow_evaluation')
        expect(alert.threshold).toBeDefined()
        expect(alert.actualValue).toBeGreaterThan(alert.threshold)
        expect(alert.severity).toMatch(/warning|critical/)
        done()
      })

      monitor.onAlert(alertCallback)

      // Simulate slow evaluation
      const slowResult = {
        ...mockResult,
        performanceMetrics: {
          ...mockResult.performanceMetrics,
          totalEvaluationTime: 600, // Exceeds critical threshold
        },
      }

      const startTime = monitor.startTiming('eval-slow')
      monitor.endTiming(mockContext, slowResult, startTime)
    })

    it('should trigger memory usage alerts', (done) => {
      // Mock process.memoryUsage for this test
      const originalProcess = global.process
      global.process = {
        ...originalProcess,
        memoryUsage: jest.fn().mockReturnValue({
          heapUsed: 60 * 1024 * 1024, // 60MB - exceeds threshold
        }),
      } as any

      const alertCallback = jest.fn((alert: PerformanceAlert) => {
        expect(alert.type).toBe('memory_usage')
        expect(alert.actualValue).toBeGreaterThan(50) // Threshold is 50MB
        global.process = originalProcess
        done()
      })

      monitor.onAlert(alertCallback)

      const startTime = monitor.startTiming('eval-memory')
      monitor.endTiming(mockContext, mockResult, startTime)
    })

    it('should handle multiple alert callbacks', () => {
      const callback1 = jest.fn()
      const callback2 = jest.fn()

      monitor.onAlert(callback1)
      monitor.onAlert(callback2)

      // Trigger an alert
      const slowResult = {
        ...mockResult,
        performanceMetrics: {
          ...mockResult.performanceMetrics,
          totalEvaluationTime: 600,
        },
      }

      const startTime = monitor.startTiming('eval-multi-alert')
      monitor.endTiming(mockContext, slowResult, startTime)

      expect(callback1).toHaveBeenCalled()
      expect(callback2).toHaveBeenCalled()
    })
  })

  describe('data management', () => {
    it('should retrieve recent data correctly', () => {
      // Add data points with different timestamps
      const now = Date.now()
      jest.spyOn(Date, 'now')
        .mockReturnValueOnce(now - 3600000) // 1 hour ago
        .mockReturnValueOnce(now - 1800000) // 30 minutes ago
        .mockReturnValueOnce(now) // Now

      for (let i = 0; i < 3; i++) {
        const startTime = monitor.startTiming(`eval-${i}`)
        monitor.endTiming(mockContext, mockResult, startTime)
      }

      const recent30min = monitor.getRecentData('link-1', 30)
      const recent60min = monitor.getRecentData('link-1', 60)

      expect(recent30min.length).toBeLessThanOrEqual(recent60min.length)
    })

    it('should clear data correctly', () => {
      const startTime = monitor.startTiming('eval-clear')
      monitor.endTiming(mockContext, mockResult, startTime)

      expect(monitor.getRecentData('link-1', 60)).toHaveLength(1)

      monitor.clearData()

      expect(monitor.getRecentData('link-1', 60)).toHaveLength(0)
      expect(monitor.getPerformanceStats('link-1').totalEvaluations).toBe(0)
    })
  })

  describe('singleton behavior', () => {
    it('should return the same instance', () => {
      const instance1 = RulePerformanceMonitor.getInstance()
      const instance2 = RulePerformanceMonitor.getInstance()

      expect(instance1).toBe(instance2)
    })

    it('should maintain state across getInstance calls', () => {
      const instance1 = RulePerformanceMonitor.getInstance()
      instance1.recordCacheHit('test-link')

      const instance2 = RulePerformanceMonitor.getInstance()
      const startTime = instance2.startTiming('eval-singleton')
      const metrics = instance2.endTiming(mockContext, mockResult, startTime)

      expect(metrics.cacheHitRate).toBeGreaterThan(0)
    })
  })
})
