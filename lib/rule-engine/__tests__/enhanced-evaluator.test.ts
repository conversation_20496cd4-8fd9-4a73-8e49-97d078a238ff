/**
 * Enhanced Rule Evaluator Tests
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { EnhancedRuleEvaluator } from '../enhanced-evaluator'
import { RulePerformanceMonitor } from '../performance-monitor'
import { <PERSON><PERSON>rror<PERSON>and<PERSON> } from '../error-handler'
import type { 
  ConditionalLinkWithConditions, 
  EnhancedLinkCondition,
  EvaluationOptions 
} from '../types'
import type { ConditionalVisitorContext } from '../../utils/conditional-visitor-context'

// Mock performance.now for consistent testing
const mockPerformanceNow = jest.fn()
Object.defineProperty(global, 'performance', {
  value: { now: mockPerformanceNow },
  writable: true,
})

describe('EnhancedRuleEvaluator', () => {
  let evaluator: EnhancedRuleEvaluator
  let mockVisitorContext: ConditionalVisitorContext
  let mockLink: ConditionalLinkWithConditions

  beforeEach(() => {
    evaluator = new EnhancedRuleEvaluator()
    
    // Reset performance monitor and error handler
    RulePerformanceMonitor.getInstance().clearData()
    RuleErrorHandler.getInstance().clearErrorLog()
    
    // Mock performance.now to return incrementing values
    let timeCounter = 0
    mockPerformanceNow.mockImplementation(() => timeCounter += 10)

    // Create mock visitor context
    mockVisitorContext = {
      device: {
        type: 'desktop',
        platform: 'Windows',
        browser: 'Chrome',
        isMobile: false,
      },
      location: {
        country: 'US',
        region: 'CA',
        city: 'San Francisco',
        timezone: 'America/Los_Angeles',
      },
      referrer: {
        domain: 'google.com',
        source: 'search',
        medium: 'organic',
        isInternal: false,
      },
      time: {
        current: new Date('2024-01-15T10:30:00Z'),
        dayOfWeek: 1, // Monday
        hour: 10,
        minute: 30,
      },
    } as ConditionalVisitorContext

    // Create mock link with conditions
    const mockCondition: EnhancedLinkCondition = {
      id: 'condition-1',
      linkId: 'link-1',
      type: 'referrer',
      priority: 10,
      isActive: true,
      rules: {
        type: 'referrer',
        domains: ['google.com'],
        matchType: 'exact',
        caseSensitive: false,
      },
      action: {
        type: 'show',
        alternateTitle: 'From Google',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      isValid: true,
      validationErrors: [],
      performanceImpact: 'low',
    }

    mockLink = {
      id: 'link-1',
      title: 'Test Link',
      url: 'https://example.com',
      icon: 'icon.png',
      isVisible: true,
      order: 1,
      isScheduled: false,
      hasConditions: true,
      conditions: [mockCondition],
      defaultBehavior: 'show',
      cacheEnabled: true,
      cacheTtl: 300000,
    }
  })

  afterEach(() => {
    evaluator.clearCache()
    jest.clearAllMocks()
  })

  describe('evaluateLink', () => {
    it('should evaluate a link with matching conditions', async () => {
      const result = await evaluator.evaluateLink(mockLink, mockVisitorContext)

      expect(result).toMatchObject({
        linkId: 'link-1',
        shouldShow: true,
        matchedConditions: ['condition-1'],
        fallbackUsed: false,
        effectiveTitle: 'From Google',
        effectiveUrl: 'https://example.com',
      })

      expect(result.appliedAction).toMatchObject({
        type: 'show',
        alternateTitle: 'From Google',
      })

      expect(result.metadata.strategy).toBe('priority')
      expect(result.evaluationTime).toBeGreaterThan(0)
    })

    it('should use default behavior when no conditions match', async () => {
      // Modify visitor context to not match any conditions
      const nonMatchingContext = {
        ...mockVisitorContext,
        referrer: {
          ...mockVisitorContext.referrer,
          domain: 'facebook.com',
        },
      }

      const result = await evaluator.evaluateLink(mockLink, nonMatchingContext)

      expect(result).toMatchObject({
        linkId: 'link-1',
        shouldShow: true, // default behavior is 'show'
        matchedConditions: [],
        fallbackUsed: false,
        effectiveTitle: 'Test Link',
        effectiveUrl: 'https://example.com',
      })

      expect(result.metadata.strategy).toBe('default')
    })

    it('should respect condition priority order', async () => {
      // Add a higher priority condition
      const highPriorityCondition: EnhancedLinkCondition = {
        ...mockLink.conditions[0],
        id: 'condition-2',
        priority: 20,
        rules: {
          type: 'device',
          deviceTypes: ['desktop'],
        },
        action: {
          type: 'show',
          alternateTitle: 'Desktop User',
        },
      }

      const linkWithMultipleConditions = {
        ...mockLink,
        conditions: [...mockLink.conditions, highPriorityCondition],
      }

      const result = await evaluator.evaluateLink(linkWithMultipleConditions, mockVisitorContext)

      // Should match the higher priority condition first
      expect(result.matchedConditions).toContain('condition-2')
      expect(result.effectiveTitle).toBe('Desktop User')
    })

    it('should handle caching correctly', async () => {
      const options: EvaluationOptions = {
        enableCaching: true,
        cacheTtl: 60000,
      }

      // First evaluation
      const result1 = await evaluator.evaluateLink(mockLink, mockVisitorContext, options)
      expect(result1.metadata.strategy).toBe('priority')

      // Second evaluation should use cache
      const result2 = await evaluator.evaluateLink(mockLink, mockVisitorContext, options)
      expect(result2.metadata.strategy).toBe('cache')

      // Results should be identical except for strategy
      expect(result1.linkId).toBe(result2.linkId)
      expect(result1.shouldShow).toBe(result2.shouldShow)
      expect(result1.effectiveTitle).toBe(result2.effectiveTitle)
    })

    it('should handle evaluation timeout', async () => {
      const options: EvaluationOptions = {
        maxEvaluationTime: 1, // Very short timeout
      }

      // Mock a slow evaluation by making performance.now return large increments
      mockPerformanceNow.mockImplementation(() => Date.now())

      const result = await evaluator.evaluateLink(mockLink, mockVisitorContext, options)

      // Should fall back due to timeout
      expect(result.fallbackUsed).toBe(true)
      expect(result.errorDetails).toContain('timeout')
    })

    it('should skip validation when requested', async () => {
      const invalidCondition: EnhancedLinkCondition = {
        ...mockLink.conditions[0],
        rules: {
          type: 'referrer',
          domains: [], // Invalid - empty domains array
          matchType: 'exact',
          caseSensitive: false,
        },
        isValid: false,
        validationErrors: ['At least one domain is required'],
      }

      const linkWithInvalidCondition = {
        ...mockLink,
        conditions: [invalidCondition],
      }

      const options: EvaluationOptions = {
        skipValidation: true,
      }

      // Should not throw validation error when skipped
      const result = await evaluator.evaluateLink(linkWithInvalidCondition, mockVisitorContext, options)
      expect(result).toBeDefined()
    })

    it('should handle debug mode correctly', async () => {
      const options: EvaluationOptions = {
        debugMode: true,
      }

      const result = await evaluator.evaluateLink(mockLink, mockVisitorContext, options)

      expect(result.metadata.debugInfo).toBeDefined()
      expect(result.metadata.debugInfo).toMatchObject({
        evaluationId: expect.any(String),
        matchedConditionId: 'condition-1',
        conditionType: 'referrer',
        conditionPriority: 10,
      })
    })
  })

  describe('evaluateLinks', () => {
    it('should evaluate multiple links efficiently', async () => {
      const link2: ConditionalLinkWithConditions = {
        ...mockLink,
        id: 'link-2',
        title: 'Second Link',
        conditions: [{
          ...mockLink.conditions[0],
          id: 'condition-3',
          linkId: 'link-2',
          rules: {
            type: 'device',
            deviceTypes: ['desktop'],
          },
        }],
      }

      const links = [mockLink, link2]
      const results = await evaluator.evaluateLinks(links, mockVisitorContext)

      expect(results).toHaveLength(2)
      expect(results[0].linkId).toBe('link-1')
      expect(results[1].linkId).toBe('link-2')
      
      // Both should show since conditions match
      expect(results[0].shouldShow).toBe(true)
      expect(results[1].shouldShow).toBe(true)
    })
  })

  describe('cache management', () => {
    it('should clear cache correctly', () => {
      evaluator.clearCache()
      const stats = evaluator.getCacheStats()
      expect(stats.size).toBe(0)
    })

    it('should provide cache statistics', async () => {
      const options: EvaluationOptions = { enableCaching: true }
      
      // Perform some evaluations
      await evaluator.evaluateLink(mockLink, mockVisitorContext, options)
      await evaluator.evaluateLink(mockLink, mockVisitorContext, options) // Cache hit

      const stats = evaluator.getCacheStats()
      expect(stats.size).toBeGreaterThan(0)
    })
  })

  describe('error handling integration', () => {
    it('should handle validation errors gracefully', async () => {
      // Create a link with invalid configuration
      const invalidLink = {
        ...mockLink,
        conditions: [{
          ...mockLink.conditions[0],
          rules: {
            type: 'referrer',
            domains: [], // Invalid
            matchType: 'exact',
            caseSensitive: false,
          },
          isValid: false,
          validationErrors: ['At least one domain is required'],
        }],
      }

      const result = await evaluator.evaluateLink(invalidLink, mockVisitorContext)
      
      // Should fall back gracefully
      expect(result.fallbackUsed).toBe(true)
      expect(result.errorDetails).toContain('validation')
    })
  })

  describe('performance monitoring integration', () => {
    it('should record performance metrics', async () => {
      await evaluator.evaluateLink(mockLink, mockVisitorContext)

      const stats = RulePerformanceMonitor.getInstance().getPerformanceStats('link-1')
      expect(stats.totalEvaluations).toBe(1)
      expect(stats.averageEvaluationTime).toBeGreaterThan(0)
    })

    it('should detect slow evaluations', async () => {
      // Mock slow evaluation
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(1000) // 1 second evaluation time

      const alertCallback = jest.fn()
      RulePerformanceMonitor.getInstance().onAlert(alertCallback)

      await evaluator.evaluateLink(mockLink, mockVisitorContext)

      // Should trigger slow evaluation alert
      expect(alertCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'slow_evaluation',
          linkId: 'link-1',
          severity: expect.any(String),
        })
      )
    })
  })
})
