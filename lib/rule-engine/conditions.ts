
import { z } from 'zod';

// Condition Types
export const ReferrerConditionSchema = z.object({
  type: z.literal('referrer'),
  match: z.enum(['exact', 'contains', 'regex']),
  value: z.string(),
});
export type ReferrerCondition = z.infer<typeof ReferrerConditionSchema>;

export const LocationConditionSchema = z.object({
  type: z.literal('location'),
  match: z.enum(['country', 'region', 'city']),
  value: z.string(),
});
export type LocationCondition = z.infer<typeof LocationConditionSchema>;

export const DeviceConditionSchema = z.object({
  type: z.literal('device'),
  match: z.enum(['type', 'platform', 'browser']),
  value: z.string(),
});
export type DeviceCondition = z.infer<typeof DeviceConditionSchema>;

export const TimeConditionSchema = z.object({
  type: z.literal('time'),
  dayOfWeek: z.number().min(0).max(6).optional(), // 0 = Sunday
  startTime: z.string().optional(), // HH:mm
  endTime: z.string().optional(), // HH:mm
});
export type TimeCondition = z.infer<typeof TimeConditionSchema>;

export const LinkConditionSchema = z.union([
  ReferrerConditionSchema,
  LocationConditionSchema,
  DeviceConditionSchema,
  TimeConditionSchema,
]);
export type LinkCondition = z.infer<typeof LinkConditionSchema>;

// Visitor Context
export const VisitorContextSchema = z.object({
  referrer: z.string().optional(),
  location: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
  }).optional(),
  device: z.object({
    type: z.string().optional(), // 'mobile', 'desktop', 'tablet'
    platform: z.string().optional(), // 'iOS', 'Android', 'Windows', 'macOS'
    browser: z.string().optional(), // 'Chrome', 'Safari', 'Firefox'
  }).optional(),
  now: z.date().optional(),
  timezone: z.string().optional(),
});
export type VisitorContext = z.infer<typeof VisitorContextSchema>;

// Evaluation Result
export const RuleEvaluationResultSchema = z.object({
  shouldShow: z.boolean(),
  matchedCondition: LinkConditionSchema.optional(),
});
export type RuleEvaluationResult = z.infer<typeof RuleEvaluationResultSchema>;
