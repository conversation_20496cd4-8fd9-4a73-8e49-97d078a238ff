/**
 * Rule Engine Constants
 * 
 * Configuration constants for the rule priority and fallback system
 */

import type { FallbackStrategy } from './types'

/**
 * Performance thresholds and limits
 */
export const PERFORMANCE_THRESHOLDS = {
  /** Maximum evaluation time in milliseconds before timeout */
  MAX_EVALUATION_TIME: 10000, // 10 seconds
  
  /** Warning threshold for evaluation time in milliseconds */
  SLOW_EVALUATION_WARNING: 100, // 100ms
  
  /** Critical threshold for evaluation time in milliseconds */
  SLOW_EVALUATION_CRITICAL: 500, // 500ms
  
  /** Maximum number of conditions per link */
  MAX_CONDITIONS_PER_LINK: 100,
  
  /** Warning threshold for number of conditions */
  HIGH_CONDITION_COUNT_WARNING: 20,
  
  /** Maximum memory usage in MB during evaluation */
  MAX_MEMORY_USAGE: 50,
  
  /** Cache TTL in milliseconds */
  CACHE_TTL: 300000, // 5 minutes
  
  /** Maximum cache size (number of entries) */
  MAX_CACHE_SIZE: 10000,
} as const

/**
 * Error handling configuration
 */
export const ERROR_HANDLING = {
  /** Maximum retries for transient errors */
  MAX_RETRIES: 3,
  
  /** Base retry delay in milliseconds */
  BASE_RETRY_DELAY: 100,
  
  /** Exponential backoff multiplier */
  RETRY_BACKOFF_MULTIPLIER: 2,
  
  /** Maximum retry delay in milliseconds */
  MAX_RETRY_DELAY: 5000,
  
  /** Error rate threshold for alerts (percentage) */
  ERROR_RATE_ALERT_THRESHOLD: 5,
  
  /** Time window for error rate calculation in milliseconds */
  ERROR_RATE_WINDOW: 300000, // 5 minutes
} as const

/**
 * Priority system configuration
 */
export const PRIORITY_SYSTEM = {
  /** Default priority for new conditions */
  DEFAULT_PRIORITY: 0,
  
  /** Minimum priority value */
  MIN_PRIORITY: -1000,
  
  /** Maximum priority value */
  MAX_PRIORITY: 1000,
  
  /** Priority increment for bulk operations */
  PRIORITY_INCREMENT: 10,
  
  /** Schedule condition priority (always highest) */
  SCHEDULE_PRIORITY: Number.MAX_SAFE_INTEGER,
} as const

/**
 * Validation configuration
 */
export const VALIDATION_CONFIG = {
  /** Maximum rule complexity score */
  MAX_COMPLEXITY_SCORE: 100,
  
  /** Warning threshold for complexity score */
  COMPLEXITY_WARNING_THRESHOLD: 50,
  
  /** Maximum regex pattern length */
  MAX_REGEX_LENGTH: 500,
  
  /** Maximum number of domains in referrer rules */
  MAX_DOMAINS_PER_RULE: 50,
  
  /** Maximum number of countries in location rules */
  MAX_COUNTRIES_PER_RULE: 20,
  
  /** Maximum condition title length */
  MAX_CONDITION_TITLE_LENGTH: 100,
  
  /** Maximum condition description length */
  MAX_CONDITION_DESCRIPTION_LENGTH: 500,
} as const

/**
 * Caching configuration
 */
export const CACHE_CONFIG = {
  /** Default cache TTL in milliseconds */
  DEFAULT_TTL: 300000, // 5 minutes
  
  /** Short cache TTL for frequently changing data */
  SHORT_TTL: 60000, // 1 minute
  
  /** Long cache TTL for stable data */
  LONG_TTL: 3600000, // 1 hour
  
  /** Cache key prefix */
  KEY_PREFIX: 'rule_eval:',
  
  /** Maximum cache key length */
  MAX_KEY_LENGTH: 250,
  
  /** Cache warming threshold */
  WARMING_THRESHOLD: 0.8,
} as const

/**
 * Monitoring and alerting configuration
 */
export const MONITORING_CONFIG = {
  /** Performance metrics collection interval in milliseconds */
  METRICS_COLLECTION_INTERVAL: 60000, // 1 minute
  
  /** Metrics retention period in milliseconds */
  METRICS_RETENTION_PERIOD: **********, // 30 days
  
  /** Alert cooldown period in milliseconds */
  ALERT_COOLDOWN_PERIOD: 300000, // 5 minutes
  
  /** Batch size for metrics processing */
  METRICS_BATCH_SIZE: 100,
  
  /** Health check interval in milliseconds */
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
} as const

/**
 * Default fallback strategies
 */
export const FALLBACK_STRATEGIES: Record<string, FallbackStrategy> = {
  /** Conservative strategy - prefer hiding on errors */
  conservative: {
    name: 'conservative',
    maxRetries: 2,
    retryDelay: 100,
    fallbackBehavior: 'hide',
    alertThreshold: 1,
    cacheFallback: true,
  },
  
  /** Balanced strategy - use default behavior on errors */
  balanced: {
    name: 'balanced',
    maxRetries: 3,
    retryDelay: 200,
    fallbackBehavior: 'default',
    alertThreshold: 5,
    cacheFallback: true,
  },
  
  /** Aggressive strategy - prefer showing on errors */
  aggressive: {
    name: 'aggressive',
    maxRetries: 1,
    retryDelay: 50,
    fallbackBehavior: 'show',
    alertThreshold: 10,
    cacheFallback: false,
  },
  
  /** Performance-focused strategy - fast fallback */
  performance: {
    name: 'performance',
    maxRetries: 1,
    retryDelay: 25,
    fallbackBehavior: 'cache',
    alertThreshold: 2,
    cacheFallback: true,
  },
} as const

/**
 * Rule evaluation modes
 */
export const EVALUATION_MODES = {
  /** Standard evaluation with full feature set */
  STANDARD: 'standard',
  
  /** Fast evaluation with minimal features */
  FAST: 'fast',
  
  /** Debug evaluation with detailed logging */
  DEBUG: 'debug',
  
  /** Safe evaluation with maximum error handling */
  SAFE: 'safe',
} as const

/**
 * Condition types and their default configurations
 */
export const CONDITION_TYPES = {
  referrer: {
    name: 'Referrer',
    description: 'Match based on referring website',
    defaultPriority: 10,
    performanceImpact: 'low',
    maxComplexity: 20,
  },
  location: {
    name: 'Location',
    description: 'Match based on visitor location',
    defaultPriority: 20,
    performanceImpact: 'medium',
    maxComplexity: 30,
  },
  device: {
    name: 'Device',
    description: 'Match based on device type and capabilities',
    defaultPriority: 15,
    performanceImpact: 'low',
    maxComplexity: 15,
  },
  time: {
    name: 'Time',
    description: 'Match based on time and date',
    defaultPriority: 25,
    performanceImpact: 'low',
    maxComplexity: 25,
  },
  schedule: {
    name: 'Schedule',
    description: 'Match based on link schedule',
    defaultPriority: PRIORITY_SYSTEM.SCHEDULE_PRIORITY,
    performanceImpact: 'low',
    maxComplexity: 10,
  },
} as const

/**
 * Error messages and codes
 */
export const ERROR_MESSAGES = {
  VALIDATION_FAILED: 'Rule validation failed',
  EVALUATION_TIMEOUT: 'Rule evaluation timed out',
  INVALID_CONFIGURATION: 'Invalid rule configuration',
  CONFLICT_DETECTED: 'Rule conflicts detected',
  PERFORMANCE_DEGRADED: 'Rule evaluation performance degraded',
  CACHE_ERROR: 'Cache operation failed',
  NETWORK_ERROR: 'Network operation failed',
  SYSTEM_ERROR: 'System error occurred',
  UNKNOWN_ERROR: 'Unknown error occurred',
} as const

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  RULE_CREATED: 'Rule created successfully',
  RULE_UPDATED: 'Rule updated successfully',
  RULE_DELETED: 'Rule deleted successfully',
  VALIDATION_PASSED: 'Rule validation passed',
  CONFLICTS_RESOLVED: 'Rule conflicts resolved',
  PERFORMANCE_OPTIMIZED: 'Rule performance optimized',
} as const

/**
 * Regular expressions for validation
 */
export const VALIDATION_PATTERNS = {
  /** Domain name pattern */
  DOMAIN: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  
  /** Time format pattern (HH:mm) */
  TIME: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
  
  /** Timezone pattern */
  TIMEZONE: /^[A-Za-z_]+\/[A-Za-z_]+$/,
  
  /** Country code pattern (ISO 3166-1 alpha-2) */
  COUNTRY_CODE: /^[A-Z]{2}$/,
  
  /** URL pattern */
  URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
  
  /** Safe string pattern (no special characters) */
  SAFE_STRING: /^[a-zA-Z0-9\s\-_.,!?()]+$/,
} as const

/**
 * Default values for various configurations
 */
export const DEFAULTS = {
  /** Default link behavior when no conditions match */
  DEFAULT_BEHAVIOR: 'show' as const,
  
  /** Default fallback behavior on errors */
  FALLBACK_BEHAVIOR: 'default' as const,
  
  /** Default cache enabled state */
  CACHE_ENABLED: true,
  
  /** Default debug mode state */
  DEBUG_MODE: false,
  
  /** Default performance monitoring state */
  PERFORMANCE_MONITORING: true,
  
  /** Default error alerting state */
  ERROR_ALERTING: true,
} as const

/**
 * Feature flags for gradual rollout
 */
export const FEATURE_FLAGS = {
  /** Enable enhanced rule evaluation */
  ENHANCED_EVALUATION: true,
  
  /** Enable performance monitoring */
  PERFORMANCE_MONITORING: true,
  
  /** Enable conflict detection */
  CONFLICT_DETECTION: true,
  
  /** Enable caching */
  CACHING_ENABLED: true,
  
  /** Enable debug mode */
  DEBUG_MODE: false,
  
  /** Enable experimental features */
  EXPERIMENTAL_FEATURES: false,
} as const
