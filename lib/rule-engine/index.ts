/**
 * Rule Engine - Main Export
 * 
 * Comprehensive rule priority and fallback system for conditional link rules
 */

// Core types
export type {
  RuleEvaluationContext,
  RuleEvaluationResult,
  PerformanceMetrics,
  EnhancedLinkCondition,
  ConditionalLinkWithConditions,
  ConditionAction,
  RuleConfiguration,
  ReferrerRule,
  LocationRule,
  DeviceRule,
  TimeRule,
  ScheduleRule,
  RuleEvaluationError,
  RuleEvaluationErrorType,
  FallbackStrategy,
  RuleValidationResult,
  ConflictDetectionResult,
  RuleConflict,
  RuleWarning,
} from './types'

// Enhanced evaluator
export { EnhancedRuleEvaluator } from './enhanced-evaluator'
export type { EvaluationOptions } from './enhanced-evaluator'

// Performance monitoring
export { RulePerformanceMonitor } from './performance-monitor'
export type { 
  PerformanceAlert,
  PerformanceStats,
  PerformanceDataPoint,
} from './performance-monitor'

// Error handling
export { RuleErrorHandler } from './error-handler'
export type {
  <PERSON>rrorRecoveryAttempt,
  ErrorContext,
} from './error-handler'

// Rule validation
export { RuleValidator } from './rule-validator'

// Conflict resolution
export { RuleConflictResolver } from './conflict-resolver'
export type { ConflictResolution } from './conflict-resolver'

// Constants and configuration
export {
  PERFORMANCE_THRESHOLDS,
  ERROR_HANDLING,
  PRIORITY_SYSTEM,
  VALIDATION_CONFIG,
  CACHE_CONFIG,
  MONITORING_CONFIG,
  FALLBACK_STRATEGIES,
  EVALUATION_MODES,
  CONDITION_TYPES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_PATTERNS,
  DEFAULTS,
  FEATURE_FLAGS,
} from './constants'

/**
 * Rule Engine Factory
 * 
 * Factory class for creating and configuring rule engine components
 */
export class RuleEngineFactory {
  /**
   * Create a new enhanced rule evaluator with default configuration
   */
  public static createEvaluator(): EnhancedRuleEvaluator {
    return new EnhancedRuleEvaluator()
  }

  /**
   * Get the performance monitor singleton
   */
  public static getPerformanceMonitor(): RulePerformanceMonitor {
    return RulePerformanceMonitor.getInstance()
  }

  /**
   * Get the error handler singleton
   */
  public static getErrorHandler(): RuleErrorHandler {
    return RuleErrorHandler.getInstance()
  }

  /**
   * Create a rule validator
   */
  public static createValidator(): typeof RuleValidator {
    return RuleValidator
  }

  /**
   * Create a conflict resolver
   */
  public static createConflictResolver(): typeof RuleConflictResolver {
    return RuleConflictResolver
  }
}

/**
 * Rule Engine Utilities
 * 
 * Utility functions for working with the rule engine
 */
export class RuleEngineUtils {
  /**
   * Validate a rule configuration
   */
  public static validateRule(rule: RuleConfiguration): RuleValidationResult {
    return RuleValidator.validateRule(rule)
  }

  /**
   * Detect conflicts in link conditions
   */
  public static detectConflicts(link: ConditionalLinkWithConditions): ConflictDetectionResult {
    return RuleConflictResolver.detectConflicts(link)
  }

  /**
   * Generate a unique evaluation ID
   */
  public static generateEvaluationId(): string {
    return `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Calculate rule complexity score
   */
  public static calculateComplexity(conditions: EnhancedLinkCondition[]): number {
    return conditions.reduce((total, condition) => {
      let complexity = 0
      
      switch (condition.rules.type) {
        case 'referrer':
          complexity += condition.rules.domains.length * 2
          if (condition.rules.matchType === 'regex') complexity += 10
          break
        case 'location':
          complexity += (condition.rules.countries?.length || 0) * 1
          complexity += (condition.rules.regions?.length || 0) * 2
          complexity += (condition.rules.cities?.length || 0) * 3
          break
        case 'device':
          complexity += (condition.rules.deviceTypes?.length || 0) * 1
          complexity += (condition.rules.platforms?.length || 0) * 2
          break
        case 'time':
          complexity += (condition.rules.daysOfWeek?.length || 0) * 1
          if (condition.rules.startTime || condition.rules.endTime) complexity += 3
          break
        case 'schedule':
          complexity += 2
          if (condition.rules.recurring) complexity += 5
          break
      }
      
      return total + complexity
    }, 0)
  }

  /**
   * Optimize condition priorities to eliminate conflicts
   */
  public static optimizePriorities(conditions: EnhancedLinkCondition[]): EnhancedLinkCondition[] {
    const sortedConditions = [...conditions].sort((a, b) => {
      // Schedule conditions first
      if (a.type === 'schedule' && b.type !== 'schedule') return -1
      if (b.type === 'schedule' && a.type !== 'schedule') return 1
      
      // Then by current priority
      return b.priority - a.priority
    })

    // Reassign priorities with gaps
    return sortedConditions.map((condition, index) => ({
      ...condition,
      priority: (sortedConditions.length - index) * 10,
    }))
  }

  /**
   * Get recommended fallback strategy based on link characteristics
   */
  public static getRecommendedFallbackStrategy(
    link: ConditionalLinkWithConditions
  ): keyof typeof FALLBACK_STRATEGIES {
    const complexity = this.calculateComplexity(link.conditions)
    const conditionCount = link.conditions.length

    // High complexity or many conditions - use conservative strategy
    if (complexity > 50 || conditionCount > 15) {
      return 'conservative'
    }

    // Performance-critical scenarios
    if (link.maxEvaluationTime && link.maxEvaluationTime < 100) {
      return 'performance'
    }

    // Default to balanced strategy
    return 'balanced'
  }

  /**
   * Create a performance-optimized link configuration
   */
  public static optimizeForPerformance(
    link: ConditionalLinkWithConditions
  ): ConditionalLinkWithConditions {
    const optimizedConditions = this.optimizePriorities(link.conditions)
    
    return {
      ...link,
      conditions: optimizedConditions,
      maxEvaluationTime: Math.min(link.maxEvaluationTime || 1000, 500),
      cacheEnabled: true,
      cacheTtl: CACHE_CONFIG.DEFAULT_TTL,
    }
  }

  /**
   * Generate a comprehensive health report for a link
   */
  public static generateHealthReport(link: ConditionalLinkWithConditions): {
    overall: 'healthy' | 'warning' | 'critical'
    complexity: number
    conflicts: ConflictDetectionResult
    validation: RuleValidationResult
    recommendations: string[]
  } {
    const complexity = this.calculateComplexity(link.conditions)
    const conflicts = this.detectConflicts(link)
    const validation = RuleValidator.validateConditions(link.conditions)
    
    const recommendations: string[] = []
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy'

    // Check complexity
    if (complexity > 50) {
      overall = 'warning'
      recommendations.push('Consider reducing rule complexity for better performance')
    }

    // Check conflicts
    if (conflicts.hasConflicts) {
      const hasErrors = conflicts.conflicts.some(c => c.severity === 'error')
      overall = hasErrors ? 'critical' : 'warning'
      recommendations.push('Resolve rule conflicts to ensure predictable behavior')
    }

    // Check validation
    if (!validation.isValid) {
      overall = 'critical'
      recommendations.push('Fix validation errors before deploying')
    } else if (validation.warnings.length > 0) {
      if (overall === 'healthy') overall = 'warning'
      recommendations.push('Address validation warnings for optimal performance')
    }

    // Performance recommendations
    if (link.conditions.length > 10) {
      recommendations.push('Consider consolidating conditions for better performance')
    }

    if (!link.cacheEnabled) {
      recommendations.push('Enable caching to improve evaluation performance')
    }

    return {
      overall,
      complexity,
      conflicts,
      validation,
      recommendations,
    }
  }
}

/**
 * Default export - Enhanced Rule Evaluator
 */
export default EnhancedRuleEvaluator
