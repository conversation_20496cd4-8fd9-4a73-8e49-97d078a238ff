/**
 * Rule Engine Error Handler
 * 
 * Handles errors during rule evaluation with comprehensive fallback strategies
 */

import type {
  RuleEvaluationError,
  RuleEvaluationErrorType,
  FallbackStrategy,
  RuleEvaluationContext,
  RuleEvaluationResult,
  ConditionAction,
} from './types'
import {
  ERROR_HANDLING,
  FALLBACK_STRATEGIES,
  ERROR_MESSAGES,
  DEFAULTS,
} from './constants'

/**
 * Error recovery attempt information
 */
export interface ErrorRecoveryAttempt {
  attemptNumber: number
  strategy: string
  timestamp: Date
  success: boolean
  errorMessage?: string
  recoveryTime: number
}

/**
 * Error context for detailed logging
 */
export interface ErrorContext {
  evaluationId: string
  linkId: string
  conditionId?: string
  visitorContext: Record<string, any>
  ruleConfiguration?: Record<string, any>
  stackTrace?: string
  userAgent?: string
  timestamp: Date
}

/**
 * Rule engine error handler class
 */
export class RuleErrorHandler {
  private static instance: RuleErrorHandler
  private errorLog: RuleEvaluationError[] = []
  private recoveryAttempts: Map<string, ErrorRecoveryAttempt[]> = new Map()
  private errorCallbacks: ((error: RuleEvaluationError) => void)[] = []
  private fallbackCache: Map<string, RuleEvaluationResult> = new Map()

  private constructor() {
    // Start periodic cleanup
    this.startPeriodicCleanup()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RuleErrorHandler {
    if (!RuleErrorHandler.instance) {
      RuleErrorHandler.instance = new RuleErrorHandler()
    }
    return RuleErrorHandler.instance
  }

  /**
   * Handle an error during rule evaluation with automatic recovery
   */
  public async handleEvaluationError(
    error: Error,
    context: RuleEvaluationContext,
    strategy: FallbackStrategy = FALLBACK_STRATEGIES.balanced
  ): Promise<RuleEvaluationResult> {
    const startTime = performance.now()
    const errorType = this.categorizeError(error)
    
    // Create error record
    const ruleError: RuleEvaluationError = {
      type: errorType,
      message: error.message,
      linkId: context.link.id,
      context: this.createErrorContext(context, error),
      timestamp: new Date(),
      stack: error.stack,
      recoverable: this.isRecoverableError(errorType),
    }

    // Log the error
    this.logError(ruleError)

    // Attempt recovery
    const recoveryResult = await this.attemptRecovery(ruleError, context, strategy)
    
    // Record recovery attempt
    this.recordRecoveryAttempt(context.evaluationId, {
      attemptNumber: this.getAttemptNumber(context.evaluationId),
      strategy: strategy.name,
      timestamp: new Date(),
      success: !recoveryResult.fallbackUsed,
      errorMessage: recoveryResult.errorDetails,
      recoveryTime: performance.now() - startTime,
    })

    return recoveryResult
  }

  /**
   * Create a safe fallback result when all recovery attempts fail
   */
  public createFallbackResult(
    context: RuleEvaluationContext,
    error: RuleEvaluationError,
    strategy: FallbackStrategy
  ): RuleEvaluationResult {
    const fallbackAction = this.determineFallbackAction(context, strategy)
    
    return {
      linkId: context.link.id,
      shouldShow: fallbackAction.type === 'show',
      matchedConditions: [],
      appliedAction: fallbackAction,
      evaluationTime: performance.now() - context.startTime,
      fallbackUsed: true,
      errorDetails: `Fallback used due to: ${error.message}`,
      performanceMetrics: {
        totalEvaluationTime: performance.now() - context.startTime,
        conditionEvaluationTimes: {},
        visitorContextParsingTime: 0,
        databaseQueryTime: 0,
        cacheHitRate: 0,
        conditionsEvaluated: 0,
      },
      effectiveTitle: context.link.title,
      effectiveIcon: context.link.icon,
      effectiveUrl: context.link.url,
      metadata: {
        strategy: 'fallback',
        skippedConditions: context.link.conditions.map(c => c.id),
        warnings: [`Fallback strategy '${strategy.name}' applied due to error`],
        debugInfo: context.debugMode ? {
          originalError: error.message,
          errorType: error.type,
          fallbackStrategy: strategy.name,
        } : undefined,
      },
    }
  }

  /**
   * Get cached fallback result if available
   */
  public getCachedFallback(cacheKey: string): RuleEvaluationResult | null {
    return this.fallbackCache.get(cacheKey) || null
  }

  /**
   * Cache a fallback result
   */
  public cacheFallbackResult(
    cacheKey: string,
    result: RuleEvaluationResult,
    ttl: number = 300000 // 5 minutes
  ): void {
    this.fallbackCache.set(cacheKey, result)
    
    // Set expiration
    setTimeout(() => {
      this.fallbackCache.delete(cacheKey)
    }, ttl)
  }

  /**
   * Subscribe to error notifications
   */
  public onError(callback: (error: RuleEvaluationError) => void): void {
    this.errorCallbacks.push(callback)
  }

  /**
   * Get error statistics
   */
  public getErrorStats(linkId?: string, timeWindow?: number): {
    totalErrors: number
    errorsByType: Record<RuleEvaluationErrorType, number>
    errorRate: number
    recoveryRate: number
    averageRecoveryTime: number
  } {
    const cutoff = timeWindow ? new Date(Date.now() - timeWindow) : new Date(0)
    const relevantErrors = this.errorLog
      .filter(e => e.timestamp >= cutoff)
      .filter(e => !linkId || e.linkId === linkId)

    const errorsByType = relevantErrors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<RuleEvaluationErrorType, number>)

    const recoveryAttempts = Array.from(this.recoveryAttempts.values()).flat()
    const successfulRecoveries = recoveryAttempts.filter(a => a.success).length
    const totalRecoveryTime = recoveryAttempts.reduce((sum, a) => sum + a.recoveryTime, 0)

    return {
      totalErrors: relevantErrors.length,
      errorsByType,
      errorRate: relevantErrors.length, // Per time window
      recoveryRate: recoveryAttempts.length > 0 ? (successfulRecoveries / recoveryAttempts.length) * 100 : 0,
      averageRecoveryTime: recoveryAttempts.length > 0 ? totalRecoveryTime / recoveryAttempts.length : 0,
    }
  }

  /**
   * Clear error logs (for testing)
   */
  public clearErrorLog(): void {
    this.errorLog = []
    this.recoveryAttempts.clear()
    this.fallbackCache.clear()
  }

  /**
   * Attempt error recovery with retries
   */
  private async attemptRecovery(
    error: RuleEvaluationError,
    context: RuleEvaluationContext,
    strategy: FallbackStrategy
  ): Promise<RuleEvaluationResult> {
    // Check if error is recoverable
    if (!error.recoverable) {
      return this.createFallbackResult(context, error, strategy)
    }

    // Try cached fallback first if strategy allows
    if (strategy.cacheFallback && context.cacheKey) {
      const cachedResult = this.getCachedFallback(context.cacheKey)
      if (cachedResult) {
        return {
          ...cachedResult,
          fallbackUsed: true,
          errorDetails: `Used cached fallback due to: ${error.message}`,
          metadata: {
            ...cachedResult.metadata,
            strategy: 'cache',
          },
        }
      }
    }

    // Attempt retries for transient errors
    if (this.isTransientError(error.type)) {
      for (let attempt = 1; attempt <= strategy.maxRetries; attempt++) {
        try {
          // Wait before retry
          if (attempt > 1) {
            await this.delay(strategy.retryDelay * Math.pow(2, attempt - 1))
          }

          // This would be the actual retry logic
          // For now, we'll simulate a retry attempt
          const retrySuccess = Math.random() > 0.7 // 30% success rate for simulation
          
          if (retrySuccess) {
            // Return a successful result
            return this.createSuccessfulRetryResult(context, attempt)
          }
        } catch (retryError) {
          // Log retry failure
          console.warn(`[RuleErrorHandler] Retry attempt ${attempt} failed:`, retryError)
          
          if (attempt === strategy.maxRetries) {
            // All retries exhausted, use fallback
            break
          }
        }
      }
    }

    // All recovery attempts failed, use fallback strategy
    const fallbackResult = this.createFallbackResult(context, error, strategy)
    
    // Cache the fallback result if strategy allows
    if (strategy.cacheFallback && context.cacheKey) {
      this.cacheFallbackResult(context.cacheKey, fallbackResult)
    }

    return fallbackResult
  }

  /**
   * Categorize error type for appropriate handling
   */
  private categorizeError(error: Error): RuleEvaluationErrorType {
    const message = error.message.toLowerCase()
    
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation_error'
    }
    if (message.includes('configuration') || message.includes('config')) {
      return 'configuration_error'
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network_error'
    }
    if (message.includes('timeout') || message.includes('timed out')) {
      return 'timeout_error'
    }
    if (message.includes('system') || message.includes('memory') || message.includes('resource')) {
      return 'system_error'
    }
    
    return 'unknown_error'
  }

  /**
   * Check if error type is recoverable
   */
  private isRecoverableError(errorType: RuleEvaluationErrorType): boolean {
    const recoverableTypes: RuleEvaluationErrorType[] = [
      'network_error',
      'timeout_error',
      'system_error',
    ]
    
    return recoverableTypes.includes(errorType)
  }

  /**
   * Check if error is transient (worth retrying)
   */
  private isTransientError(errorType: RuleEvaluationErrorType): boolean {
    const transientTypes: RuleEvaluationErrorType[] = [
      'network_error',
      'timeout_error',
      'system_error',
    ]
    
    return transientTypes.includes(errorType)
  }

  /**
   * Determine appropriate fallback action
   */
  private determineFallbackAction(
    context: RuleEvaluationContext,
    strategy: FallbackStrategy
  ): ConditionAction {
    switch (strategy.fallbackBehavior) {
      case 'show':
        return { type: 'show' }
      case 'hide':
        return { type: 'hide' }
      case 'default':
        return { type: context.link.defaultBehavior }
      case 'cache':
        // Try to use cached result, fall back to default if not available
        return { type: context.link.defaultBehavior }
      default:
        return { type: DEFAULTS.DEFAULT_BEHAVIOR }
    }
  }

  /**
   * Create error context for logging
   */
  private createErrorContext(context: RuleEvaluationContext, error: Error): Record<string, any> {
    return {
      evaluationId: context.evaluationId,
      linkId: context.link.id,
      linkTitle: context.link.title,
      conditionCount: context.link.conditions.length,
      visitorContext: {
        deviceType: context.visitorContext.device.type,
        country: context.visitorContext.location.country,
        referrerDomain: context.visitorContext.referrer.domain,
      },
      timestamp: context.currentTime.toISOString(),
      debugMode: context.debugMode,
      errorName: error.name,
      errorMessage: error.message,
    }
  }

  /**
   * Log error and notify callbacks
   */
  private logError(error: RuleEvaluationError): void {
    this.errorLog.push(error)
    
    // Limit log size
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-500)
    }

    // Notify error callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error)
      } catch (callbackError) {
        console.error('[RuleErrorHandler] Error in error callback:', callbackError)
      }
    })

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('[RuleErrorHandler] Rule evaluation error:', error)
    }
  }

  /**
   * Record recovery attempt
   */
  private recordRecoveryAttempt(evaluationId: string, attempt: ErrorRecoveryAttempt): void {
    const attempts = this.recoveryAttempts.get(evaluationId) || []
    attempts.push(attempt)
    this.recoveryAttempts.set(evaluationId, attempts)
  }

  /**
   * Get current attempt number for evaluation
   */
  private getAttemptNumber(evaluationId: string): number {
    const attempts = this.recoveryAttempts.get(evaluationId) || []
    return attempts.length + 1
  }

  /**
   * Create successful retry result
   */
  private createSuccessfulRetryResult(
    context: RuleEvaluationContext,
    attemptNumber: number
  ): RuleEvaluationResult {
    return {
      linkId: context.link.id,
      shouldShow: context.link.defaultBehavior === 'show',
      matchedConditions: [],
      appliedAction: { type: context.link.defaultBehavior },
      evaluationTime: performance.now() - context.startTime,
      fallbackUsed: false,
      performanceMetrics: {
        totalEvaluationTime: performance.now() - context.startTime,
        conditionEvaluationTimes: {},
        visitorContextParsingTime: 0,
        databaseQueryTime: 0,
        cacheHitRate: 0,
        conditionsEvaluated: 0,
      },
      effectiveTitle: context.link.title,
      effectiveIcon: context.link.icon,
      effectiveUrl: context.link.url,
      metadata: {
        strategy: 'retry',
        skippedConditions: [],
        warnings: [`Recovered after ${attemptNumber} attempts`],
        debugInfo: context.debugMode ? {
          retryAttempt: attemptNumber,
        } : undefined,
      },
    }
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Start periodic cleanup of old data
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOldData()
    }, 3600000) // 1 hour
  }

  /**
   * Clean up old error data
   */
  private cleanupOldData(): void {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours
    
    // Clean up error log
    this.errorLog = this.errorLog.filter(e => e.timestamp >= cutoff)
    
    // Clean up recovery attempts
    for (const [evaluationId, attempts] of this.recoveryAttempts.entries()) {
      const recentAttempts = attempts.filter(a => a.timestamp >= cutoff)
      if (recentAttempts.length === 0) {
        this.recoveryAttempts.delete(evaluationId)
      } else {
        this.recoveryAttempts.set(evaluationId, recentAttempts)
      }
    }
  }
}
