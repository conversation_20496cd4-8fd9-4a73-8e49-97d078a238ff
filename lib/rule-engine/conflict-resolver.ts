/**
 * Rule Conflict Resolver
 * 
 * Detects and resolves conflicts between conditional rules
 */

import type {
  ConflictDetectionResult,
  RuleConflict,
  RuleWarning,
  EnhancedLinkCondition,
  ConditionalLinkWithConditions,
  RuleConfiguration,
  ConditionAction,
} from './types'
import { PRIORITY_SYSTEM } from './constants'

/**
 * Conflict resolution suggestion
 */
export interface ConflictResolution {
  conflictId: string
  type: 'priority_adjustment' | 'rule_modification' | 'rule_removal' | 'action_change'
  description: string
  suggestedChanges: {
    conditionId: string
    changes: Partial<EnhancedLinkCondition>
  }[]
  impact: 'low' | 'medium' | 'high'
  automatic: boolean
}

/**
 * Rule conflict resolver class
 */
export class RuleConflictResolver {
  /**
   * Detect conflicts in a set of conditions
   */
  public static detectConflicts(
    link: ConditionalLinkWithConditions
  ): ConflictDetectionResult {
    const conflicts: RuleConflict[] = []
    const warnings: RuleWarning[] = []
    const suggestions: string[] = []

    const activeConditions = link.conditions.filter(c => c.isActive)

    // Detect unreachable rules
    const unreachableConflicts = this.detectUnreachableRules(activeConditions)
    conflicts.push(...unreachableConflicts)

    // Detect contradictory actions
    const contradictoryConflicts = this.detectContradictoryActions(activeConditions)
    conflicts.push(...contradictoryConflicts)

    // Detect circular dependencies
    const circularConflicts = this.detectCircularDependencies(activeConditions)
    conflicts.push(...circularConflicts)

    // Detect performance issues
    const performanceWarnings = this.detectPerformanceIssues(activeConditions)
    warnings.push(...performanceWarnings)

    // Generate suggestions
    suggestions.push(...this.generateSuggestions(conflicts, warnings, link))

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
      warnings,
      suggestions,
    }
  }

  /**
   * Generate automatic conflict resolutions
   */
  public static generateResolutions(
    conflicts: RuleConflict[],
    link: ConditionalLinkWithConditions
  ): ConflictResolution[] {
    const resolutions: ConflictResolution[] = []

    for (const conflict of conflicts) {
      switch (conflict.type) {
        case 'unreachable':
          resolutions.push(...this.resolveUnreachableRules(conflict, link))
          break
        case 'contradictory':
          resolutions.push(...this.resolveContradictoryActions(conflict, link))
          break
        case 'performance':
          resolutions.push(...this.resolvePerformanceIssues(conflict, link))
          break
      }
    }

    return resolutions
  }

  /**
   * Apply automatic resolutions
   */
  public static applyResolutions(
    resolutions: ConflictResolution[],
    link: ConditionalLinkWithConditions
  ): ConditionalLinkWithConditions {
    const updatedLink = { ...link }
    const updatedConditions = [...link.conditions]

    for (const resolution of resolutions.filter(r => r.automatic)) {
      for (const change of resolution.suggestedChanges) {
        const conditionIndex = updatedConditions.findIndex(c => c.id === change.conditionId)
        if (conditionIndex !== -1) {
          updatedConditions[conditionIndex] = {
            ...updatedConditions[conditionIndex],
            ...change.changes,
          }
        }
      }
    }

    updatedLink.conditions = updatedConditions
    return updatedLink
  }

  /**
   * Detect unreachable rules (rules that can never be triggered due to higher priority rules)
   */
  private static detectUnreachableRules(
    conditions: EnhancedLinkCondition[]
  ): RuleConflict[] {
    const conflicts: RuleConflict[] = []
    const sortedConditions = [...conditions].sort((a, b) => b.priority - a.priority)

    for (let i = 0; i < sortedConditions.length; i++) {
      const currentCondition = sortedConditions[i]
      
      // Check if any higher priority condition makes this one unreachable
      for (let j = 0; j < i; j++) {
        const higherPriorityCondition = sortedConditions[j]
        
        if (this.isConditionSubsetOf(currentCondition, higherPriorityCondition)) {
          conflicts.push({
            type: 'unreachable',
            affectedRules: [currentCondition.id],
            description: `Condition "${currentCondition.id}" is unreachable because condition "${higherPriorityCondition.id}" has higher priority and covers the same or broader criteria`,
            severity: 'warning',
            resolution: `Increase priority of condition "${currentCondition.id}" or modify its criteria to be more specific`,
          })
        }
      }
    }

    return conflicts
  }

  /**
   * Detect contradictory actions (rules with conflicting actions for overlapping conditions)
   */
  private static detectContradictoryActions(
    conditions: EnhancedLinkCondition[]
  ): RuleConflict[] {
    const conflicts: RuleConflict[] = []

    for (let i = 0; i < conditions.length; i++) {
      for (let j = i + 1; j < conditions.length; j++) {
        const condition1 = conditions[i]
        const condition2 = conditions[j]

        if (this.conditionsOverlap(condition1, condition2)) {
          const action1 = condition1.action
          const action2 = condition2.action

          if (this.actionsConflict(action1, action2)) {
            conflicts.push({
              type: 'contradictory',
              affectedRules: [condition1.id, condition2.id],
              description: `Conditions "${condition1.id}" and "${condition2.id}" have overlapping criteria but contradictory actions`,
              severity: 'error',
              resolution: 'Adjust priorities or modify conditions to eliminate overlap',
            })
          }
        }
      }
    }

    return conflicts
  }

  /**
   * Detect circular dependencies (not applicable to current rule system, but included for completeness)
   */
  private static detectCircularDependencies(
    conditions: EnhancedLinkCondition[]
  ): RuleConflict[] {
    // Current rule system doesn't support dependencies between rules
    // This is a placeholder for future enhancements
    return []
  }

  /**
   * Detect performance issues
   */
  private static detectPerformanceIssues(
    conditions: EnhancedLinkCondition[]
  ): RuleWarning[] {
    const warnings: RuleWarning[] = []

    // Check for too many conditions
    if (conditions.length > 20) {
      warnings.push({
        type: 'performance',
        message: `High number of conditions (${conditions.length}) may impact evaluation performance`,
        ruleId: 'all',
        severity: 'medium',
        suggestion: 'Consider consolidating similar conditions or using more specific criteria',
      })
    }

    // Check for complex regex patterns
    for (const condition of conditions) {
      if (condition.rules.type === 'referrer' && condition.rules.matchType === 'regex') {
        for (const domain of condition.rules.domains) {
          if (domain.length > 100 || this.isComplexRegex(domain)) {
            warnings.push({
              type: 'performance',
              message: `Complex regex pattern may impact performance: ${domain}`,
              ruleId: condition.id,
              severity: 'medium',
              suggestion: 'Consider simplifying the regex pattern or using exact/contains matching',
            })
          }
        }
      }
    }

    return warnings
  }

  /**
   * Generate general suggestions for improvement
   */
  private static generateSuggestions(
    conflicts: RuleConflict[],
    warnings: RuleWarning[],
    link: ConditionalLinkWithConditions
  ): string[] {
    const suggestions: string[] = []

    if (conflicts.length > 0) {
      suggestions.push('Review rule priorities to ensure intended evaluation order')
      suggestions.push('Consider consolidating overlapping rules for better maintainability')
    }

    if (warnings.length > 0) {
      suggestions.push('Optimize rule complexity for better performance')
    }

    const priorities = link.conditions.map(c => c.priority)
    const uniquePriorities = new Set(priorities)
    if (uniquePriorities.size < priorities.length) {
      suggestions.push('Assign unique priorities to all conditions for deterministic evaluation')
    }

    return suggestions
  }

  /**
   * Resolve unreachable rules
   */
  private static resolveUnreachableRules(
    conflict: RuleConflict,
    link: ConditionalLinkWithConditions
  ): ConflictResolution[] {
    const resolutions: ConflictResolution[] = []
    const affectedCondition = link.conditions.find(c => c.id === conflict.affectedRules[0])

    if (affectedCondition) {
      // Suggest priority adjustment
      resolutions.push({
        conflictId: `unreachable_${affectedCondition.id}`,
        type: 'priority_adjustment',
        description: 'Increase priority to make rule reachable',
        suggestedChanges: [{
          conditionId: affectedCondition.id,
          changes: {
            priority: this.findAvailablePriority(link.conditions, affectedCondition.priority + 10),
          },
        }],
        impact: 'low',
        automatic: false, // Require manual approval for priority changes
      })

      // Suggest rule modification to be more specific
      resolutions.push({
        conflictId: `unreachable_modify_${affectedCondition.id}`,
        type: 'rule_modification',
        description: 'Make rule more specific to avoid overlap',
        suggestedChanges: [{
          conditionId: affectedCondition.id,
          changes: {
            // This would need specific logic based on rule type
          },
        }],
        impact: 'medium',
        automatic: false,
      })
    }

    return resolutions
  }

  /**
   * Resolve contradictory actions
   */
  private static resolveContradictoryActions(
    conflict: RuleConflict,
    link: ConditionalLinkWithConditions
  ): ConflictResolution[] {
    const resolutions: ConflictResolution[] = []
    const [ruleId1, ruleId2] = conflict.affectedRules
    const condition1 = link.conditions.find(c => c.id === ruleId1)
    const condition2 = link.conditions.find(c => c.id === ruleId2)

    if (condition1 && condition2) {
      // Suggest priority-based resolution
      if (condition1.priority === condition2.priority) {
        resolutions.push({
          conflictId: `contradictory_priority_${ruleId1}_${ruleId2}`,
          type: 'priority_adjustment',
          description: 'Adjust priorities to resolve action conflict',
          suggestedChanges: [
            {
              conditionId: ruleId1,
              changes: { priority: condition1.priority + 1 },
            },
          ],
          impact: 'low',
          automatic: true,
        })
      }
    }

    return resolutions
  }

  /**
   * Resolve performance issues
   */
  private static resolvePerformanceIssues(
    conflict: RuleConflict,
    link: ConditionalLinkWithConditions
  ): ConflictResolution[] {
    const resolutions: ConflictResolution[] = []

    // This would contain specific performance optimization suggestions
    // based on the type of performance issue detected

    return resolutions
  }

  /**
   * Check if one condition is a subset of another
   */
  private static isConditionSubsetOf(
    condition1: EnhancedLinkCondition,
    condition2: EnhancedLinkCondition
  ): boolean {
    // This would need specific logic for each rule type
    // For now, return false as a placeholder
    return false
  }

  /**
   * Check if two conditions overlap
   */
  private static conditionsOverlap(
    condition1: EnhancedLinkCondition,
    condition2: EnhancedLinkCondition
  ): boolean {
    // This would need specific logic for each rule type
    // For now, return false as a placeholder
    return false
  }

  /**
   * Check if two actions conflict
   */
  private static actionsConflict(action1: ConditionAction, action2: ConditionAction): boolean {
    // Actions conflict if they have different types (show vs hide)
    return action1.type !== action2.type
  }

  /**
   * Check if a regex pattern is complex
   */
  private static isComplexRegex(pattern: string): boolean {
    // Check for patterns that might cause performance issues
    const complexPatterns = [
      /\(\.\*\)/,     // (.*)
      /\.\*\.\*/,     // .*.*
      /\+\+/,         // ++
      /\*\*/,         // **
      /\{\d+,\}/,     // {n,} quantifiers
    ]

    return complexPatterns.some(complex => complex.test(pattern))
  }

  /**
   * Find an available priority value
   */
  private static findAvailablePriority(
    conditions: EnhancedLinkCondition[],
    preferredPriority: number
  ): number {
    const usedPriorities = new Set(conditions.map(c => c.priority))
    
    let priority = preferredPriority
    while (usedPriorities.has(priority)) {
      priority++
    }
    
    return Math.min(priority, PRIORITY_SYSTEM.MAX_PRIORITY)
  }
}
