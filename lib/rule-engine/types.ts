/**
 * Enhanced Rule Engine Types
 * 
 * Type definitions for the rule priority and fallback system
 */

import type { LinkCondition as PrismaLinkCondition } from '@prisma/client'
import type { ConditionalVisitorContext } from '../utils/conditional-visitor-context'

/**
 * Rule evaluation context containing all necessary information for evaluation
 */
export interface RuleEvaluationContext {
  /** Unique identifier for this evaluation session */
  evaluationId: string
  
  /** Link being evaluated with its conditions */
  link: ConditionalLinkWithConditions
  
  /** Visitor context for condition matching */
  visitorContext: ConditionalVisitorContext
  
  /** Evaluation start timestamp for performance tracking */
  startTime: number
  
  /** Current time for time-based conditions */
  currentTime: Date
  
  /** Cache key for result caching */
  cacheKey?: string
  
  /** Debug mode flag for detailed logging */
  debugMode?: boolean
}

/**
 * Performance metrics collected during rule evaluation
 */
export interface PerformanceMetrics {
  /** Total evaluation time in milliseconds */
  totalEvaluationTime: number
  
  /** Time spent evaluating each condition by ID */
  conditionEvaluationTimes: Record<string, number>
  
  /** Time spent parsing visitor context */
  visitorContextParsingTime: number
  
  /** Time spent on database queries */
  databaseQueryTime: number
  
  /** Cache hit rate for this evaluation */
  cacheHitRate: number
  
  /** Number of conditions evaluated */
  conditionsEvaluated: number
  
  /** Memory usage during evaluation */
  memoryUsage?: number
}

/**
 * Enhanced rule evaluation result with comprehensive information
 */
export interface RuleEvaluationResult {
  /** Link ID that was evaluated */
  linkId: string
  
  /** Whether the link should be shown */
  shouldShow: boolean
  
  /** IDs of conditions that matched */
  matchedConditions: string[]
  
  /** Action that was applied */
  appliedAction: ConditionAction
  
  /** Total evaluation time in milliseconds */
  evaluationTime: number
  
  /** Whether fallback behavior was used */
  fallbackUsed: boolean
  
  /** Error details if evaluation failed */
  errorDetails?: string
  
  /** Performance metrics for this evaluation */
  performanceMetrics: PerformanceMetrics
  
  /** Effective title after rule application */
  effectiveTitle: string
  
  /** Effective icon after rule application */
  effectiveIcon?: string
  
  /** Effective URL after rule application */
  effectiveUrl: string
  
  /** Evaluation metadata */
  metadata: {
    /** Evaluation strategy used */
    strategy: 'priority' | 'fallback' | 'default' | 'cache'
    
    /** Conditions that were skipped */
    skippedConditions: string[]
    
    /** Warnings generated during evaluation */
    warnings: string[]
    
    /** Debug information if debug mode enabled */
    debugInfo?: Record<string, any>
  }
}

/**
 * Condition action when rule matches
 */
export interface ConditionAction {
  type: 'show' | 'hide' | 'redirect'
  value?: string // For redirect actions
  alternateTitle?: string
  alternateIcon?: string
  metadata?: Record<string, any>
}

/**
 * Enhanced link condition with priority and validation
 */
export interface EnhancedLinkCondition extends Omit<PrismaLinkCondition, 'rules' | 'action'> {
  rules: RuleConfiguration
  action: ConditionAction
  
  /** Validation status */
  isValid: boolean
  
  /** Validation errors if any */
  validationErrors: string[]
  
  /** Performance impact rating */
  performanceImpact: 'low' | 'medium' | 'high'
  
  /** Last evaluation time */
  lastEvaluated?: Date
  
  /** Success rate for this condition */
  successRate?: number
}

/**
 * Link with enhanced conditions
 */
export interface ConditionalLinkWithConditions {
  id: string
  title: string
  url: string
  icon?: string
  isVisible: boolean
  order: number
  
  // Scheduling fields
  isScheduled: boolean
  scheduleStart?: Date
  scheduleEnd?: Date
  timezone?: string
  
  // Conditional rules
  hasConditions: boolean
  conditions: EnhancedLinkCondition[]
  defaultBehavior: 'show' | 'hide'
  
  // Enhanced fields
  fallbackBehavior?: 'show' | 'hide' | 'default'
  maxEvaluationTime?: number
  cacheEnabled?: boolean
  cacheTtl?: number
}

/**
 * Rule configuration union type
 */
export type RuleConfiguration = 
  | ReferrerRule 
  | LocationRule 
  | DeviceRule 
  | TimeRule 
  | ScheduleRule

/**
 * Referrer-based rule configuration
 */
export interface ReferrerRule {
  type: 'referrer'
  domains: string[]
  matchType: 'exact' | 'contains' | 'regex'
  caseSensitive: boolean
  excludeDomains?: string[]
}

/**
 * Location-based rule configuration
 */
export interface LocationRule {
  type: 'location'
  countries?: string[]
  regions?: string[]
  cities?: string[]
  timezones?: string[]
  excludeCountries?: string[]
  excludeRegions?: string[]
  excludeCities?: string[]
}

/**
 * Device-based rule configuration
 */
export interface DeviceRule {
  type: 'device'
  deviceTypes?: ('mobile' | 'tablet' | 'desktop')[]
  platforms?: string[]
  browsers?: string[]
  excludeDeviceTypes?: ('mobile' | 'tablet' | 'desktop')[]
  excludePlatforms?: string[]
  excludeBrowsers?: string[]
}

/**
 * Time-based rule configuration
 */
export interface TimeRule {
  type: 'time'
  daysOfWeek?: number[] // 0-6, Sunday = 0
  startTime?: string // HH:mm format
  endTime?: string // HH:mm format
  timezone?: string
  dateRange?: {
    start: Date
    end: Date
  }
}

/**
 * Schedule-based rule configuration
 */
export interface ScheduleRule {
  type: 'schedule'
  scheduleStart: Date
  scheduleEnd?: Date
  timezone: string
  recurring?: {
    pattern: 'daily' | 'weekly' | 'monthly'
    interval: number
    endDate?: Date
  }
}

/**
 * Error categories for rule evaluation
 */
export type RuleEvaluationErrorType = 
  | 'validation_error'
  | 'configuration_error'
  | 'network_error'
  | 'timeout_error'
  | 'system_error'
  | 'unknown_error'

/**
 * Rule evaluation error with context
 */
export interface RuleEvaluationError {
  type: RuleEvaluationErrorType
  message: string
  conditionId?: string
  linkId: string
  context: Record<string, any>
  timestamp: Date
  stack?: string
  recoverable: boolean
}

/**
 * Fallback strategy configuration
 */
export interface FallbackStrategy {
  /** Strategy name */
  name: string
  
  /** Maximum retries before fallback */
  maxRetries: number
  
  /** Retry delay in milliseconds */
  retryDelay: number
  
  /** Fallback behavior when all retries exhausted */
  fallbackBehavior: 'show' | 'hide' | 'cache' | 'default'
  
  /** Alert threshold for error rate */
  alertThreshold: number
  
  /** Whether to cache fallback results */
  cacheFallback: boolean
}

/**
 * Rule validation result
 */
export interface RuleValidationResult {
  /** Whether the rule is valid */
  isValid: boolean
  
  /** Validation errors */
  errors: string[]
  
  /** Validation warnings */
  warnings: string[]
  
  /** Performance impact assessment */
  performanceImpact: 'low' | 'medium' | 'high'
  
  /** Suggestions for improvement */
  suggestions: string[]
}

/**
 * Conflict detection result
 */
export interface ConflictDetectionResult {
  /** Whether conflicts were found */
  hasConflicts: boolean
  
  /** Detected conflicts */
  conflicts: RuleConflict[]
  
  /** Warnings about potential issues */
  warnings: RuleWarning[]
  
  /** Suggestions for resolution */
  suggestions: string[]
}

/**
 * Rule conflict information
 */
export interface RuleConflict {
  /** Conflict type */
  type: 'unreachable' | 'contradictory' | 'circular' | 'performance'
  
  /** Affected rule IDs */
  affectedRules: string[]
  
  /** Conflict description */
  description: string
  
  /** Severity level */
  severity: 'error' | 'warning' | 'info'
  
  /** Suggested resolution */
  resolution?: string
}

/**
 * Rule warning information
 */
export interface RuleWarning {
  /** Warning type */
  type: 'performance' | 'complexity' | 'maintenance' | 'security'
  
  /** Warning message */
  message: string
  
  /** Affected rule ID */
  ruleId: string
  
  /** Severity level */
  severity: 'low' | 'medium' | 'high'
  
  /** Suggested action */
  suggestion?: string
}
