/**
 * Rule Engine Performance Monitor
 * 
 * Monitors and tracks performance metrics for rule evaluation
 */

import { performance } from 'perf_hooks'
import type { 
  PerformanceMetrics, 
  RuleEvaluationContext, 
  RuleEvaluationResult 
} from './types'
import { 
  PERFORMANCE_THRESHOLDS, 
  MONITORING_CONFIG,
  ERROR_HANDLING 
} from './constants'

/**
 * Performance alert information
 */
export interface PerformanceAlert {
  type: 'slow_evaluation' | 'high_error_rate' | 'cache_miss_spike' | 'memory_usage'
  threshold: number
  actualValue: number
  linkId: string
  conditionId?: string
  timestamp: Date
  severity: 'warning' | 'critical'
  metadata?: Record<string, any>
}

/**
 * Performance statistics aggregation
 */
export interface PerformanceStats {
  totalEvaluations: number
  averageEvaluationTime: number
  p50EvaluationTime: number
  p95EvaluationTime: number
  p99EvaluationTime: number
  errorRate: number
  cacheHitRate: number
  slowEvaluationCount: number
  timeoutCount: number
  lastUpdated: Date
}

/**
 * Performance data point for time series analysis
 */
export interface PerformanceDataPoint {
  timestamp: Date
  linkId: string
  conditionId?: string
  evaluationTime: number
  success: boolean
  cacheHit: boolean
  errorType?: string
  memoryUsage?: number
}

/**
 * Performance monitor class for tracking rule evaluation metrics
 */
export class RulePerformanceMonitor {
  private static instance: RulePerformanceMonitor
  private performanceData: PerformanceDataPoint[] = []
  private alertCallbacks: ((alert: PerformanceAlert) => void)[] = []
  private lastCleanup: Date = new Date()
  private evaluationTimes: Map<string, number[]> = new Map()
  private errorCounts: Map<string, number> = new Map()
  private cacheStats: { hits: number; misses: number } = { hits: 0, misses: 0 }

  private constructor() {
    // Start periodic cleanup
    this.startPeriodicCleanup()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RulePerformanceMonitor {
    if (!RulePerformanceMonitor.instance) {
      RulePerformanceMonitor.instance = new RulePerformanceMonitor()
    }
    return RulePerformanceMonitor.instance
  }

  /**
   * Start timing for a rule evaluation
   */
  public startTiming(evaluationId: string): number {
    return performance.now()
  }

  /**
   * End timing and record performance data
   */
  public endTiming(
    context: RuleEvaluationContext,
    result: RuleEvaluationResult,
    startTime: number
  ): PerformanceMetrics {
    const endTime = performance.now()
    const totalTime = endTime - startTime

    // Create performance metrics
    const metrics: PerformanceMetrics = {
      totalEvaluationTime: totalTime,
      conditionEvaluationTimes: result.performanceMetrics?.conditionEvaluationTimes || {},
      visitorContextParsingTime: result.performanceMetrics?.visitorContextParsingTime || 0,
      databaseQueryTime: result.performanceMetrics?.databaseQueryTime || 0,
      cacheHitRate: this.calculateCacheHitRate(),
      conditionsEvaluated: result.matchedConditions.length,
      memoryUsage: this.getMemoryUsage(),
    }

    // Record performance data point
    const dataPoint: PerformanceDataPoint = {
      timestamp: new Date(),
      linkId: context.link.id,
      evaluationTime: totalTime,
      success: !result.fallbackUsed && !result.errorDetails,
      cacheHit: result.metadata.strategy === 'cache',
      errorType: result.errorDetails ? this.categorizeError(result.errorDetails) : undefined,
      memoryUsage: metrics.memoryUsage,
    }

    this.recordDataPoint(dataPoint)
    this.checkPerformanceThresholds(context, metrics)

    return metrics
  }

  /**
   * Record cache hit
   */
  public recordCacheHit(linkId: string): void {
    this.cacheStats.hits++
  }

  /**
   * Record cache miss
   */
  public recordCacheMiss(linkId: string): void {
    this.cacheStats.misses++
  }

  /**
   * Record evaluation error
   */
  public recordError(linkId: string, errorType: string, conditionId?: string): void {
    const key = `${linkId}:${errorType}`
    const currentCount = this.errorCounts.get(key) || 0
    this.errorCounts.set(key, currentCount + 1)

    // Check error rate threshold
    this.checkErrorRateThreshold(linkId, errorType)
  }

  /**
   * Get performance statistics for a link
   */
  public getPerformanceStats(linkId?: string): PerformanceStats {
    const relevantData = linkId 
      ? this.performanceData.filter(d => d.linkId === linkId)
      : this.performanceData

    if (relevantData.length === 0) {
      return {
        totalEvaluations: 0,
        averageEvaluationTime: 0,
        p50EvaluationTime: 0,
        p95EvaluationTime: 0,
        p99EvaluationTime: 0,
        errorRate: 0,
        cacheHitRate: 0,
        slowEvaluationCount: 0,
        timeoutCount: 0,
        lastUpdated: new Date(),
      }
    }

    const evaluationTimes = relevantData.map(d => d.evaluationTime).sort((a, b) => a - b)
    const successfulEvaluations = relevantData.filter(d => d.success).length
    const cacheHits = relevantData.filter(d => d.cacheHit).length
    const slowEvaluations = relevantData.filter(d => d.evaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING).length
    const timeouts = relevantData.filter(d => d.evaluationTime > PERFORMANCE_THRESHOLDS.MAX_EVALUATION_TIME).length

    return {
      totalEvaluations: relevantData.length,
      averageEvaluationTime: evaluationTimes.reduce((sum, time) => sum + time, 0) / evaluationTimes.length,
      p50EvaluationTime: this.calculatePercentile(evaluationTimes, 0.5),
      p95EvaluationTime: this.calculatePercentile(evaluationTimes, 0.95),
      p99EvaluationTime: this.calculatePercentile(evaluationTimes, 0.99),
      errorRate: ((relevantData.length - successfulEvaluations) / relevantData.length) * 100,
      cacheHitRate: (cacheHits / relevantData.length) * 100,
      slowEvaluationCount: slowEvaluations,
      timeoutCount: timeouts,
      lastUpdated: new Date(),
    }
  }

  /**
   * Subscribe to performance alerts
   */
  public onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertCallbacks.push(callback)
  }

  /**
   * Get recent performance data points
   */
  public getRecentData(linkId?: string, minutes: number = 60): PerformanceDataPoint[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000)
    return this.performanceData
      .filter(d => d.timestamp >= cutoff)
      .filter(d => !linkId || d.linkId === linkId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * Clear performance data (for testing)
   */
  public clearData(): void {
    this.performanceData = []
    this.evaluationTimes.clear()
    this.errorCounts.clear()
    this.cacheStats = { hits: 0, misses: 0 }
  }

  /**
   * Record a performance data point
   */
  private recordDataPoint(dataPoint: PerformanceDataPoint): void {
    this.performanceData.push(dataPoint)

    // Update evaluation times map
    const times = this.evaluationTimes.get(dataPoint.linkId) || []
    times.push(dataPoint.evaluationTime)
    this.evaluationTimes.set(dataPoint.linkId, times)

    // Limit data size
    if (this.performanceData.length > MONITORING_CONFIG.METRICS_BATCH_SIZE * 100) {
      this.performanceData = this.performanceData.slice(-MONITORING_CONFIG.METRICS_BATCH_SIZE * 50)
    }
  }

  /**
   * Check performance thresholds and trigger alerts
   */
  private checkPerformanceThresholds(
    context: RuleEvaluationContext,
    metrics: PerformanceMetrics
  ): void {
    // Check evaluation time thresholds
    if (metrics.totalEvaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_CRITICAL) {
      this.triggerAlert({
        type: 'slow_evaluation',
        threshold: PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_CRITICAL,
        actualValue: metrics.totalEvaluationTime,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'critical',
        metadata: { evaluationId: context.evaluationId },
      })
    } else if (metrics.totalEvaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING) {
      this.triggerAlert({
        type: 'slow_evaluation',
        threshold: PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING,
        actualValue: metrics.totalEvaluationTime,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'warning',
        metadata: { evaluationId: context.evaluationId },
      })
    }

    // Check memory usage
    if (metrics.memoryUsage && metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE) {
      this.triggerAlert({
        type: 'memory_usage',
        threshold: PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE,
        actualValue: metrics.memoryUsage,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'warning',
        metadata: { evaluationId: context.evaluationId },
      })
    }
  }

  /**
   * Check error rate threshold
   */
  private checkErrorRateThreshold(linkId: string, errorType: string): void {
    const recentData = this.getRecentData(linkId, 5) // Last 5 minutes
    const errorCount = recentData.filter(d => d.errorType === errorType).length
    const totalCount = recentData.length

    if (totalCount > 0) {
      const errorRate = (errorCount / totalCount) * 100
      if (errorRate > ERROR_HANDLING.ERROR_RATE_ALERT_THRESHOLD) {
        this.triggerAlert({
          type: 'high_error_rate',
          threshold: ERROR_HANDLING.ERROR_RATE_ALERT_THRESHOLD,
          actualValue: errorRate,
          linkId,
          timestamp: new Date(),
          severity: 'critical',
          metadata: { errorType, errorCount, totalCount },
        })
      }
    }
  }

  /**
   * Trigger a performance alert
   */
  private triggerAlert(alert: PerformanceAlert): void {
    console.warn(`[RulePerformanceMonitor] Alert: ${alert.type}`, alert)
    
    this.alertCallbacks.forEach(callback => {
      try {
        callback(alert)
      } catch (error) {
        console.error('[RulePerformanceMonitor] Error in alert callback:', error)
      }
    })
  }

  /**
   * Calculate cache hit rate
   */
  private calculateCacheHitRate(): number {
    const total = this.cacheStats.hits + this.cacheStats.misses
    return total > 0 ? (this.cacheStats.hits / total) * 100 : 0
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed / 1024 / 1024 // Convert to MB
    }
    return 0
  }

  /**
   * Calculate percentile from sorted array
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0
    
    const index = Math.ceil(sortedArray.length * percentile) - 1
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))]
  }

  /**
   * Categorize error type
   */
  private categorizeError(errorMessage: string): string {
    if (errorMessage.includes('timeout')) return 'timeout'
    if (errorMessage.includes('validation')) return 'validation'
    if (errorMessage.includes('network')) return 'network'
    if (errorMessage.includes('cache')) return 'cache'
    return 'unknown'
  }

  /**
   * Start periodic cleanup of old data
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOldData()
    }, MONITORING_CONFIG.METRICS_COLLECTION_INTERVAL)
  }

  /**
   * Clean up old performance data
   */
  private cleanupOldData(): void {
    const cutoff = new Date(Date.now() - MONITORING_CONFIG.METRICS_RETENTION_PERIOD)
    
    this.performanceData = this.performanceData.filter(d => d.timestamp >= cutoff)
    
    // Clean up evaluation times map
    for (const [linkId, times] of this.evaluationTimes.entries()) {
      if (times.length > 1000) {
        this.evaluationTimes.set(linkId, times.slice(-500))
      }
    }
    
    this.lastCleanup = new Date()
  }
}
